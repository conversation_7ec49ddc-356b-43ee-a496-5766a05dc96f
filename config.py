import os
from dotenv import load_dotenv

# 加载环境变量
load_dotenv()

# 获取程序运行目录
BASE_DIR = os.path.dirname(os.path.abspath(__file__))

# 使用相对路径
IMAGE_DIR = os.path.join(BASE_DIR, 'images')
CONFIG_FILE = os.path.join(BASE_DIR, 'account_config.json')

# 数据库配置
MYSQL_CONFIG = {
    'host': 'rm-bp1m7j5m7u7rgrve5ao.mysql.rds.aliyuncs.com',
    'port': int(os.getenv('MYSQL_PORT', 3306)),
    'user': 'root',
    'password': 'wj5201314@',
    'database': 'waiguan'
}

# Redis配置
REDIS_CONFIG = {
    'host': os.getenv('REDIS_HOST', '*************'),
    'port': int(os.getenv('REDIS_PORT', 6379)),
    'password': os.getenv('REDIS_PASSWORD', 'wj5201314'),
    'db': int(os.getenv('REDIS_DB', 0))
}

# RabbitMQ配置
# 可通过环境变量 MQ_ENV 控制：test=测试环境，prod=生产环境
MQ_ENV = os.getenv('MQ_ENV', 'test')  # 默认使用测试环境

if MQ_ENV == 'prod':
    # 生产环境配置
    RABBITMQ_CONFIG = {
        'host': '************',
        'port': 5672,
        'username': 'waiguan',
        'password': 'waiguan',
        'virtual_host': 'JCcccHost',
        'queue_name': 'python',
        'exchange': 'python',
        'routing_key': 'python',
        'java_update_queue': 'TestDirectQueue',
        'java_update_exchange': 'TestDirectExchange',
        'java_update_routing_key': 'TestDirectRouting'
    }
else:
    # 测试环境配置
    RABBITMQ_CONFIG = {
        'host': '************',
        'port': 5672,
        'username': 'waiguan',
        'password': 'waiguan',
        'virtual_host': 'JCcccHost-dev',
        'queue_name': 'python_test',
        'exchange': 'python_test',
        'routing_key': 'python_test',
        'java_update_queue': 'TestDirectQueue_test',
        'java_update_exchange': 'TestDirectExchange_test',
        'java_update_routing_key': 'TestDirectRouting_test'
    }

print(f"当前MQ环境: {MQ_ENV}, 队列: {RABBITMQ_CONFIG['queue_name']}")

# 腾讯云OCR配置（已废弃，使用UMI_OCR_CONFIG）
TENCENT_OCR_CONFIG = {
    'secret_id': os.getenv('TENCENT_SECRET_ID', ''),  # 请在环境变量中设置
    'secret_key': os.getenv('TENCENT_SECRET_KEY', ''),  # 请在环境变量中设置
    'region': 'ap-beijing'  # 可根据需要修改地域
}

# Umi-OCR配置
UMI_OCR_CONFIG = {
    'executable_path': os.getenv('UMI_OCR_PATH', os.path.join(os.path.dirname(__file__), 'Umi-OCR.exe')),
    'api_url': os.getenv('UMI_OCR_API_URL', 'http://127.0.0.1:1224'),
    'use_api': os.getenv('UMI_OCR_USE_API', 'true').lower() == 'true',
    'language': os.getenv('UMI_OCR_LANGUAGE', 'chinese'),
    'engine': os.getenv('UMI_OCR_ENGINE', 'paddle'),
    'confidence_threshold': float(os.getenv('UMI_OCR_CONFIDENCE', '0.7'))
}

# 游戏配置
GAME_CONFIG = {
    'executable_path': os.getenv('GAME_EXECUTABLE_PATH', 'D:\\SeasunGame\\Game\\JX3_WJ\\bin\\vk_mb\\bin64_m\\JX3ClientX3DX64.exe'),
    'launcher_window_class': 'Qt5152QWindowIcon',
    'launcher_window_title': '',
    'launcher_window_size': {'width': 1296, 'height': 759},
    'game_startup_delay': 25  # 游戏启动等待时间（秒）
}