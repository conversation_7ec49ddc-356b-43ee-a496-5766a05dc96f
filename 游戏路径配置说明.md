# 游戏路径配置说明

## 概述
游戏路径现已移至配置文件中，便于管理和修改，无需修改源代码。

## 配置方法

### 方法1：通过环境变量配置（推荐）

1. 复制 `.env.example` 文件为 `.env`
2. 在 `.env` 文件中修改游戏路径：

```env
# 游戏配置
# 游戏可执行文件路径 - 请根据实际安装路径修改
GAME_EXECUTABLE_PATH=D:\SeasunGame\Game\JX3_WJ\bin\vk_mb\bin64_m\JX3ClientX3DX64.exe
```

### 方法2：直接修改config.py文件

在 `config.py` 文件中找到 `GAME_CONFIG` 配置项，修改 `executable_path` 的默认值：

```python
# 游戏配置
GAME_CONFIG = {
    'executable_path': os.getenv('GAME_EXECUTABLE_PATH', '你的游戏路径'),
    'launcher_window_class': 'Qt5152QWindowIcon',
    'launcher_window_title': '',
    'launcher_window_size': {'width': 1296, 'height': 759},
    'game_startup_delay': 25  # 游戏启动等待时间（秒）
}
```

## 配置项说明

| 配置项 | 说明 | 默认值 |
|--------|------|--------|
| `executable_path` | 游戏可执行文件的完整路径 | `D:\SeasunGame\Game\JX3_WJ\bin\vk_mb\bin64_m\JX3ClientX3DX64.exe` |
| `launcher_window_class` | 启动器窗口类名 | `Qt5152QWindowIcon` |
| `launcher_window_title` | 启动器窗口标题 | 空字符串 |
| `launcher_window_size` | 启动器窗口大小 | 宽度1296，高度759 |
| `game_startup_delay` | 游戏启动后等待时间（秒） | 25秒 |

## 常见游戏路径示例

### 剑网3
```
D:\SeasunGame\Game\JX3_WJ\bin\vk_mb\bin64_m\JX3ClientX3DX64.exe
```

### 其他路径示例
```
C:\Program Files\SeasunGame\Game\JX3_WJ\bin\vk_mb\bin64_m\JX3ClientX3DX64.exe
E:\Games\JX3\bin\vk_mb\bin64_m\JX3ClientX3DX64.exe
```

## 注意事项

1. **路径格式**：Windows路径中的反斜杠 `\` 需要使用双反斜杠 `\\` 或正斜杠 `/`
2. **文件存在性**：确保指定的游戏可执行文件确实存在
3. **权限问题**：确保程序有权限访问游戏安装目录
4. **环境变量优先级**：环境变量配置优先于config.py中的默认值

## 验证配置

配置完成后，可以通过以下方式验证：

1. 启动程序，查看日志输出
2. 检查游戏是否能正常启动
3. 观察窗口大小是否符合配置

如果遇到问题，请检查：
- 游戏路径是否正确
- 文件是否存在
- 是否有足够的权限访问文件
