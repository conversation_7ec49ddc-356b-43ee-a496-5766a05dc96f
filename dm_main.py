import os
import sys
import time
from dm.大漠中文版 import 注册大漠_简
from database import DatabaseManager
from mq_listener import MQListener, DeliveryNotificationSender
import threading
import dashscope
from http import HTTPStatus
from datetime import datetime
import sqlite3
import json
import mysql.connector
import base64
import pika

from config import MYSQL_CONFIG, RABBITMQ_CONFIG, GAME_CONFIG
import requests
from requests_toolbelt import MultipartEncoder

# OCR相关导入，添加错误处理
try:
    import ddddocr
    OCR_AVAILABLE = True
    print("✅ OCR功能可用")
except ImportError as e:
    print(f"⚠️ OCR功能不可用: {str(e)}")
    print("⚠️ 将使用备用方案，部分功能可能受限")
    OCR_AVAILABLE = False
    ddddocr = None
from umi_ocr import UmiOCR
from qianniu_client import send_qianniu_notification
import os

import re

# 检查Python版本是否为32位
if sys.maxsize > 2**32:
    raise SystemError('必须使用32位Python版本！当前为64位Python')

class GameScript:
    def __init__(self, stop_event=None):
        # 初始化大漠
        print("初始化大漠插件...")
        self.dm = 注册大漠_简('lumiku2fdc744d96597f65888674a63fb3489a', 'yk38979202')
        if not self.dm:
            raise Exception("大漠插件初始化失败")
        
        print("正在初始化OCR...")
        # 初始化ddddocr用于一般识别
        if OCR_AVAILABLE:
            self.ocr = ddddocr.DdddOcr(beta=True)
            print("✅ ddddocr初始化成功")
        else:
            self.ocr = None
            print("⚠️ ddddocr不可用，OCR功能将受限")
        # 初始化Umi-OCR用于外观名称识别
        print("正在初始化Umi-OCR（用于外观名称识别）...")
        try:
            self.umi_ocr = UmiOCR()
            print("Umi-OCR初始化成功")
        except Exception as e:
            print(f"Umi-OCR初始化失败: {e}")
            print("将使用ddddocr作为备用方案")
            self.umi_ocr = None
        # 设置全局路径
        current_path = os.path.dirname(os.path.abspath(__file__))
        self.dm.SetPath(current_path)

        # 加载账号配置
        self.account_config = self.load_account_config()

        self.game_hwnd = None
        self.db_manager = None
        self.mq_listener = None
        self.current_guild = None
        self.current_appearance = None
        self.sendCount = None
        self.current_name = None
        self.character_name = None
        self.order_id = None
        self.last_account = None
        self.last_region = None
        self.last_guild = None
        self.buyer_wangwang = None
        self.stop_event = stop_event if stop_event is not None else threading.Event()  # 保存停止标志，如果没有传入则创建一个新的
        print(f"🔧 GameScript初始化: stop_event类型={type(self.stop_event)}, 是否为None={self.stop_event is None}")
        self.server_name = None  # 添加服务器名称属性
        self.guild_name = None  # 添加帮会名称属性
        self.image_path1 = None
        self.image_path2 = None
        print("初始化完成")
        self.init_db()

    def safe_ocr_classification(self, image_data, fallback_text=""):
        """安全的OCR识别，处理OCR不可用的情况"""
        if self.ocr is None:
            print(f"⚠️ OCR不可用，返回备用文本: '{fallback_text}'")
            return fallback_text

        try:
            result = self.ocr.classification(image_data)
            return result
        except Exception as e:
            print(f"⚠️ OCR识别失败: {str(e)}")
            return fallback_text

    def init_db(self):
        """初始化数据库"""
        self.conn = sqlite3.connect('game_status.db')
        self.cursor = self.conn.cursor()
        self.cursor.execute('''
            CREATE TABLE IF NOT EXISTS status (
                id INTEGER PRIMARY KEY AUTOINCREMENT,
                last_account TEXT,
                last_region TEXT,
                last_guild TEXT,
                timestamp DATETIME DEFAULT CURRENT_TIMESTAMP
            )
        ''')
        self.conn.commit()


    def write_status_to_file(self, message):
        """将当前状态写入到文件"""
        with open("game_status.txt", "a") as f:
            f.write(f"last_account: {message.account}, last_region: {message.region}, last_guild: {message.guild}\n")

    def initialize(self):
        """初始化数据库和MQ连接"""
        try:
            print("初始化数据库和MQ连接...")
            self.db_manager = DatabaseManager()
            self.mq_listener = MQListener(self)  # 传递GameScript实例
            
            if not self.db_manager.connect_mysql():
                return False
            if not self.db_manager.connect_redis():
                return False
            if not self.mq_listener.connect():
                return False
            
            # 添加心跳检测
            def check_connection():
                while True:
                    try:
                        if not self.mq_listener.is_connected():
                            print("MQ连接已断开，准备重连...")
                            raise Exception("MQ连接断开")
                        time.sleep(5)  # 每5秒检查一次
                    except:
                        break
                    
            # 启动心跳检测线程
            self.heartbeat_thread = threading.Thread(target=check_connection, daemon=True)
            self.heartbeat_thread.start()
            
            return True
        except Exception as e:
            print(f"初始化失败: {str(e)}")
            return False

    def cleanup(self):
        """清理资源"""
        try:
            if self.mq_listener:
                try:
                    # 完全关闭MQ连接
                    self.mq_listener.channel.stop_consuming()
                    self.mq_listener.channel.close()
                    self.mq_listener.connection.close()
                    self.mq_listener = None
                except:
                    pass
            
            if self.db_manager:
                self.db_manager.close_connections()
            
            if self.game_hwnd:
                self.dm.UnBindWindow()
            
        except Exception as e:
            print(f"清理资源失败: {str(e)}")

    def find_game_window(self):
        """查找游戏窗口"""
        print("查找游戏窗口...")
        hwnd = self.dm.FindWindow("KGWin32App", "")
        if hwnd:
            print(f"找到游戏窗口，句柄: {hwnd}")
            self.game_hwnd = hwnd

            # 绑定窗口
            if not self.bind_window():
                print("绑定窗口失败")
                return 0
                
            # 绑定成功后，将窗口置顶
            result = self.dm.SetWindowState(hwnd, 8)  # 8表示置顶
            print(f"窗口已置顶{result}")

            # 移动窗口到左上角
            result = self.dm.MoveWindow(hwnd, 0, 0)
            
            return hwnd
        print("未找到游戏窗口")
        return 0

    def bind_window(self):
        """绑定窗口"""
        if not self.game_hwnd:
            return False
        
        print("绑定窗口...")
        result = self.dm.BindWindow(self.game_hwnd, "normal", "windows", "windows", 0)
        if result == 1:
            print(f"窗口绑定成功，使用模式: normal, windows, windows")
            self.dm.SetWindowSize(self.game_hwnd, GAME_CONFIG['launcher_window_size']['width'], GAME_CONFIG['launcher_window_size']['height'])
            return True
        
        return False

    def login_game(self, account, password):
        """登录游戏"""
        try:
            hwnd = self.dm.FindWindow("KGWin32App", "")
            if hwnd:
                result = self.dm.BindWindow(hwnd, "normal", "normal", "normal", 0)
                if result == 1:
                    print(f"登录窗口绑定成功，使用模式: normal, normal, normal")
                else:
                    print(f"登录窗口绑定失败，使用模式: normal, normal, normal")
            
            print(f"准备登录账号: {account}")
            
            
            # 点击账号输入框
            print("点击账号输入框...")
            if not self.safe_move_and_click(698, 261):
                raise Exception("点击账号输入框失败")
            
            # 清除现有账号
            print("清除现有账号...")
            for _ in range(20):
                self.dm.KeyPress(8)  # Backspace
                time.sleep(0.1)
            
            # 输入账号
            print(f"输入账号: {account}")
            self.dm.KeyPressStr(account,50)
            time.sleep(0.5)
            
            print("点击密码输入框...")
            if not self.safe_move_and_click(647, 335):
                raise Exception("点击密码输入框失败")
            
            # 输入密码
            print(f"输入密码...")
            self.dm.KeyPressStr(password,50)
            time.sleep(0.5)
            
            # 点击登录
            if not self.safe_move_and_click(639, 411, click_delay=1.0):
                raise Exception("点击登录按钮失败")

            self.dm.Capture(720,387,762,410,"login.png")
            time.sleep(0.5)
            print("正在识别图片...")
            with open("login.png", 'rb') as f:
                result1 = self.ocr.classification(f.read())
                print(f"识别结果: {result1}")
                if '同意' in result1:
                    print("点击同意")
                    self.safe_move_and_click(720, 387)
            print("登录操作完成")

            self.find_game_window()
            return True
            
        except Exception as e:
            print(f"登录失败: {str(e)}")
            return False

    def find_pic_retry(self, x1, y1, x2, y2, pic_name, delta_color="202020", initial_sim=1, dir=0, retry_times=8, retry_interval=0.3):
        """
        找图重试方法
        
        Args:
            x1, y1, x2, y2: 查找区域坐标
            pic_name: 图片名称
            delta_color: 色偏，默认"202020"
            initial_sim: 初始相似度，默认1
            dir: 查找方向，默认0
            retry_times: 重试次数，默认3次
            retry_interval: 重试间隔，默认0.5秒
        
        Returns:
            tuple: (ret, x, y) 其中ret为-1表示未找到，否则返回找到的图片索引
        """
        sim = initial_sim  # 使用初始相似度
        for i in range(retry_times):
            result = self.dm.FindPic(x1, y1, x2, y2, pic_name, delta_color, sim, dir)
            if result[0] != -1:
                return result
            print(f"找图中。。。当前相似度: {sim}")
            time.sleep(retry_interval)
            sim = max(0.8, sim - 0.01)  # 递减相似度 最小为0.8
        return result
        

    def select_game_server(self, region):
        """选择游戏大区"""
        try:
            print(f"准备选择大区: {region}")
            time.sleep(6)
            if not self.safe_move_and_click(942, 77):
                raise Exception("点击游戏窗口失败")
            if not self.safe_move_and_click(942, 77):
                raise Exception("再次点击游戏窗口失败")
            time.sleep(1)

            print("点击更改服务器按钮...")
            if not self.safe_move_and_click(785, 403, click_delay=1.0):
                raise Exception("点击更改服务器按钮失败")
            if not self.safe_move_and_click(785, 403, click_delay=1.0):
                raise Exception("再次点击更改服务器按钮失败")
            
            
            # 如果region为"天鹅坪"或者"破阵子"，则点击双线区，否则点击电信区
            if region in ["天鹅坪", "破阵子"]:
                print("点击双线区...")
                if not self.safe_move_and_click(91, 360, click_delay=1.0):
                    raise Exception("点击双线区失败")
            else:
                # 找图 点击电信区
                print("点击电信区...")
                if not self.safe_move_and_click(99, 295, click_delay=1.0):
                    raise Exception("点击电信区失败")
            
            # 在服务器列表区域循环识字找到对应的服务器
            for i in range(3):
                for j in range(4):
                    x1 = 259 + 262*j
                    y1 = 90 + 55*i
                    x2 = 347 + 262*j
                    y2 = 114 + 55*i
                    print(f"截图范围：{x1},{y1},{x2},{y2}")
                    self.dm.Capture(x1,y1,x2,y2,"server.png")
                    time.sleep(0.5)
                    print("正在识别图片...")
                    with open("server.png", 'rb') as f:
                        result1 = self.ocr.classification(f.read())
                        print(f"识别结果: {result1}")
                        if self.region in result1:
                            print(f"找到{self.region}")
                            if not self.safe_move_and_click(x1, y1):
                                raise Exception(f"点击服务器{self.region}失败")

                            if not self.safe_move_and_click(1106, 668):
                                raise Exception("点击确认按钮失败")
                            
                            print("大区选择完成")
                            time.sleep(2)
                            
                            # 点击进入游戏
                            print("点击进入游戏...")
                            if not self.safe_move_and_click(635, 496, click_delay=5.0):
                                raise Exception("点击进入游戏失败")
                            if not self.safe_move_and_click(1116, 664):
                                raise Exception("点击进入游戏确认失败")
                            
                            # 等待15秒加载游戏
                            print("等待游戏加载...")
                            for i in range(15):
                                print(f"加载中 {i+1}/15")
                                self.safe_move_and_click(859, 654, click_delay=1.0)
                            
                            return True
            print(f"选择大区失败: 找不到")
            return False
        except Exception as e:
            print(f"选择大区失败: {str(e)}")
            return False

    def check_stop(self):
        """检查是否需要停止"""
        try:
            if not hasattr(self, 'stop_event'):
                print("❌ 警告: stop_event属性不存在，创建新的Event")
                self.stop_event = threading.Event()
                return False

            if self.stop_event is None:
                print("❌ 警告: stop_event为None，创建新的Event")
                self.stop_event = threading.Event()
                return False

            if self.stop_event.is_set():
                print("收到停止信号")
                return True
            return False
        except Exception as e:
            print(f"❌ check_stop方法异常: {str(e)}")
            # 创建新的stop_event作为备用
            self.stop_event = threading.Event()
            return False

    def execute_delivery_task(self):
        """执行发货任务"""
        try:
            print("开始发货任务...")
            
            # 在关键操作点检查停止信号
            if self.check_stop():
                return False
                
            # 点击背包按钮
            print("点击背包按钮成功")
            if not self.safe_move_and_click(1051, 39, click_delay=1.0):
                raise Exception("点击背包按钮失败")

            # 点击仓库按钮
            print("点击仓库按钮成功")
            if not self.safe_move_and_click(836, 671, click_delay=1.0):
                raise Exception("点击仓库按钮失败")

            # 点击帮会按钮
            print("点击帮会按钮成功")
            if not self.safe_move_and_click(39, 281, click_delay=1.0):
                raise Exception("点击帮会按钮失败")

            # 判断是否进入解锁流程
            self.dm.Capture(588,540,691,567,"lock.png")
            time.sleep(0.5)
            print("正在识别图片...")
            with open("lock.png", 'rb') as f:
                result1 = self.ocr.classification(f.read())
                print(f"识别结果: {result1}")
            
                # 如果找到锁住图片，则进入解锁流程
                if '一键解锁' in result1:
                    print("进入解锁流程...")
                    # 点击解锁按钮
                    if not self.safe_move_and_click(588, 540):
                        raise Exception("点击解锁按钮失败")

                    # 等待解锁完成：每5秒检测锁住的图片是否消失
                    for i in range(30):
                        print(f"等待解锁完成 {i+1}/30")
                        time.sleep(5)
                        # 查找是否已经解锁成功
                        self.dm.Capture(588,540,691,567,"lock.png")
                        time.sleep(0.5)
                        print("正在识别图片...")
                        with open("lock.png", 'rb') as f:
                            result1 = self.ocr.classification(f.read())
                            print(f"识别结果: {result1}")
                        if '解锁' not in result1:
                            #解锁成功，点击esc，再点击一次帮会
                            self.dm.KeyPress(27)
                            time.sleep(0.5)

                            if not self.safe_move_and_click(836, 671, click_delay=1.0):
                                raise Exception("解锁后点击仓库按钮失败")
                            if not self.safe_move_and_click(39, 281, click_delay=1.0):
                                raise Exception("解锁后点击帮会按钮失败")
                            break


                        if i == 29:
                            raise Exception("解锁失败")
                else:
                    print("未进入解锁流程")

            
            # 执行后续流程
            self.execute_after_guild1()
            print("发货任务完成")
            return True
            
        except Exception as e:
            raise Exception(f"发货任务失败: {str(e)}")

    def handle_game_message(self, message):
        """处理游戏消息"""
        try:
            print(f"🎯 开始处理游戏消息: {message.order_id}")
            print(f"⏰ 处理时间: {datetime.now().strftime('%H:%M:%S')}")

            # 从文件中读取当前状态
            self.read_status_from_file()

            # 打印当前状态
            print(f"📊 当前状态: last_account={self.last_account}, last_region={self.last_region}, last_guild={self.last_guild}")
            print(f"📨 收到游戏任务消息:")
            print(f"  账号: {message.account}")
            print(f"  大区: {message.region}")
            print(f"  帮会: {message.guild}")
            print(f"  外观: {message.appearance}")
            print(f"  数量: {message.sendCount}")
            print(f"  角色名称: {message.character_name}")
            print(f"  订单ID: {message.order_id}")

            print(f"是否拆分: {message.is_split}")
            print(f"礼盒名称: {message.gift_name}")
            print(f"礼盒颜色: {message.gift_color}")
            print(f"其余散件: {message.other_items}")
            print(f"买家旺旺: {message.buyer_wangwang}")
            print(f"客服名称: {message.user_nick}")
            print(f"淘宝订单: {message.taobao_id}")
            print(f"是否重启: {message.restart}")

            self.current_guild = message.guild
            self.current_appearance = message.appearance
            self.character_name = message.character_name
            self.sendCount = message.sendCount
            self.region = message.region
            self.account = message.account
            self.order_id = message.order_id
            self.is_split = message.is_split
            self.gift_name = message.gift_name
            self.gift_color = message.gift_color
            self.other_items = message.other_items
            self.buyer_wangwang = message.buyer_wangwang
            self.user_nick = message.user_nick
            self.taobao_id = message.taobao_id
            self.restart = message.restart

            # 如果订单ID不为空，就先检查订单的field3是不是等于3，如果查不到订单，或者field3等于3，就直接结束，并消费掉消息
            if message.order_id:
                print(f"🔍 检查订单状态: {message.order_id}")
                try:
                    # 连接数据库查询订单状态
                    connection = mysql.connector.connect(**MYSQL_CONFIG)
                    cursor = connection.cursor()

                    # 查询订单的field3字段
                    query = "SELECT field3 FROM orders WHERE order_id = %s"
                    cursor.execute(query, (message.order_id,))
                    result = cursor.fetchone()

                    cursor.close()
                    connection.close()

                    if result is None:
                        print(f"⚠️ 订单 {message.order_id} 不存在，跳过处理")
                        return  # 直接返回，消费掉消息

                    field3_value = result[0]
                    print(f"📊 订单 {message.order_id} 的field3值: {field3_value}")

                    if field3_value == 3:
                        print(f"✅ 订单 {message.order_id} 已完成(field3=3)，跳过处理")
                        return  # 直接返回，消费掉消息

                    print(f"🚀 订单 {message.order_id} 状态正常，继续处理")

                except mysql.connector.Error as db_error:
                    print(f"❌ 数据库查询失败: {str(db_error)}")
                    # 数据库错误时也跳过处理，避免重复执行
                    return
                except Exception as e:
                    print(f"❌ 检查订单状态时出错: {str(e)}")
                    return

            # 检查是否需要重新登录
            need_relogin = (
                self.last_account is not None and  # 不是第一次运行
                (message.account != self.last_account or  # 账号不同
                 message.region != self.last_region or    # 大区不同
                 message.guild != self.last_guild or      # 帮会不同
                 message.restart)                         # 强制重启
            )

            print(f"🔍 登录检查结果:")
            print(f"  是否第一次运行: {self.last_account is None}")
            print(f"  账号是否相同: {message.account == self.last_account}")
            print(f"  大区是否相同: {message.region == self.last_region}")
            print(f"  帮会是否相同: {message.guild == self.last_guild}")
            print(f"  是否强制重启: {message.restart}")
            print(f"  需要重新登录: {need_relogin}")
            
            # 检查游戏是否已启动
            self.game_hwnd = self.find_game_window()

            #激活窗口
            self.dm.SetWindowState(self.game_hwnd, 1)
            
            if not self.game_hwnd:
                print("游戏未启动，请先启动游戏...")
                raise Exception("游戏未启动，请先启动游戏...")
            
            if self.restart:
                print("🔄 强制重启")
                self.end_process()
                self.start_game()
            
            # 登录游戏
            if need_relogin:
                print("🔄 需要重新登录，检测当前页面是否是游戏中！")
                result = self.find_pic_retry(
                    0,0,2000,2000,
                    "images/待登录按钮.bmp"
                )
                if result[0] == -1:
                    print("需要重新登录...")
                    if not self.logout_game():
                        print("退出游戏失败，终止操作")
                        raise Exception("退出游戏失败，终止操作")
                # 重新登录
                if not self.login_game(message.account, message.password):
                    print("重新登录失败，终止操作")
                    raise Exception("重新登录失败，终止操作")
                    # 重新选择游戏服务器
                if not self.select_game_server(message.region):
                    print("重新选择游戏服务器失败，终止操作")
                    raise Exception("重新选择游戏服务器失败，终止操作")
            else:
                print("✅ 不需要退出登录，检查当前页面状态")
                # 检测当前页面是否是登录页面
                time.sleep(5)
                result = self.find_pic_retry(
                    0,0,2000,2000,
                    "images/待登录按钮.bmp",
                    "202020"
                )
                if result[0] != -1:
                    print("⚠️ 当前页面是登录页面，需要登录")
                    # 执行登录流程
                    if not self.login_game(message.account, message.password):
                        print("❌ 登录失败，终止操作")
                        raise Exception("登录失败，终止操作")
                    # 重新选择游戏服务器
                    if not self.select_game_server(message.region):
                        print("❌ 选择游戏服务器失败，终止操作")
                        raise Exception("选择游戏服务器失败，终止操作")
                else:
                    print("✅ 当前页面不是登录页面，已在游戏中")
            
            # 写入当前状态到文件
            self.write_status_to_file(message)

            for i in range(5):
                # 点击esc
                print("点击esc...")
                self.dm.KeyPress(27)
                time.sleep(0.5)

                self.dm.Capture(1222,688,1256,705,"logout.png")
                time.sleep(0.5)
                print("正在识别图片...")
                with open("logout.png", 'rb') as f:
                    result1 = self.ocr.classification(f.read())
                    print(f"识别结果: {result1}")
                    if '退出' in result1:
                        # 点击esc
                        print("点击esc...")
                        self.dm.KeyPress(27)
                        time.sleep(0.5)
                        break
            # 执行完整的发货任务
            if not self.execute_delivery_task():
                print("发货任务失败，终止操作")
                raise Exception("发货任务失败，终止操作")
                    
            #更新订单
            self.update_order_status(
                order_id=self.order_id,
                character_name=self.character_name,
                image_path=[self.image_path1,self.image_path2],
                is_success=True

            )


        except Exception as e:
            print(f"处理消息失败: {str(e)}")
            # 更新失败状态
            self.update_order_status(
                order_id=self.order_id,
                is_success=False,
                error_message=str(e)
            )
            return

    def end_process(self):
        """结束进程"""
        try:
            print("结束进程...")
            pid = self.dm.GetWindowProcessId(self.game_hwnd)
            self.dm.TerminateProcess(pid)
        except Exception as e:
            print(f"结束进程失败: {str(e)}")
            raise Exception("结束进程失败")
    def start_game(self):
        """启动游戏"""
        try:
            print("启动游戏...")
            time.sleep(5)
            hwnd = self.dm.RunApp(GAME_CONFIG['executable_path'], 1)
            print(f"游戏窗口句柄: {hwnd}")
            if hwnd == 0:
                raise Exception("启动游戏失败")
            time.sleep(GAME_CONFIG['game_startup_delay'])

            self.find_game_window()
        except Exception as e:
            print(f"唤醒启动器失败: {e}")
            raise Exception("唤醒启动器失败")
    def read_status_from_file(self):
        """从文件中读取当前状态"""
        if os.path.exists("game_status.txt"):
            with open("game_status.txt", "r") as f:
                lines = f.readlines()
                if lines:
                    last_line = lines[-1].strip()
                    parts = last_line.split(", ")
                    for part in parts:
                        key, value = part.split(": ")
                        if key == "last_account":
                            self.last_account = value
                        elif key == "last_region":
                            self.last_region = value
                        elif key == "last_guild":
                            self.last_guild = value

    def logout_game(self):
        """退出游戏到登录界面"""
        try:
            time.sleep(3)
            print("开始退出游戏流程...")
            self.dm.SetWindowState(self.game_hwnd, 1)
            
            for i in range(5):
                # 点击esc
                print("点击esc...")
                self.dm.KeyPress(27)
                time.sleep(0.5)

                self.dm.Capture(1222,688,1256,705,"logout.png")
                time.sleep(0.5)
                print("正在识别图片...")
                with open("logout.png", 'rb') as f:
                    result1 = self.ocr.classification(f.read())
                    print(f"识别结果: {result1}")
                    if '退出' in result1:
                        break
            print("点击退出")
            if not self.safe_move_and_click(1240, 675, click_delay=1.0):
                raise Exception("点击退出按钮失败")

            print("点击返回登录")
            if not self.safe_move_and_click(636, 538, click_delay=3.0):
                raise Exception("点击返回登录失败")

            for i in range(2):
                if not self.safe_move_and_click(938, 29, click_delay=1.0):
                    print(f"第{i+1}次点击关闭按钮失败")

            print("点击切换账号")
            if not self.safe_move_and_click(1240, 115, click_delay=1.5):
                raise Exception("点击切换账号失败")

            self.dm.BindWindow(self.game_hwnd, "normal", "normal", "normal", 0)
            print("点击其他方式登录")
            if not self.safe_move_and_click(765, 500, click_delay=1):
                raise Exception("点击其他方式登录失败")

            return True
            
        except Exception as e:
            print(f"退出游戏失败: {str(e)}")
            return False

    def script_main(self):
        """脚本的主入口方法"""
        try:
            print("开始执行游戏自动化脚本...")
            
            while True:  # 添加无限循环
                try:
                    # 确保完全清理旧连接
                    try:
                        self.cleanup()
                    except:
                        pass
                    
                    time.sleep(1)  # 等待旧连接完全关闭
                    
                    # 初始化连接
                    if not self.initialize():
                        print("初始化连接失败，5秒后重试...")
                        time.sleep(5)
                        continue
                        
                    # 初始化时查找游戏窗口
                    hwnd = self.find_game_window()
                    if hwnd:
                        print(f"游戏已经运行，窗口句柄: {hwnd}")
                        if not self.bind_window():
                            print("初始绑定窗口失败，5秒后重试...")
                            time.sleep(5)
                            continue
                    
                    # 启动消息监听
                    def message_handler(message):
                        self.handle_game_message(message)

                    print("开始监听游戏任务消息...")
                    self.mq_listener.start_listening(message_handler)
                    
                except Exception as e:
                    print(f"连接异常，5秒后重试: {str(e)}")
                    time.sleep(5)
                
        except KeyboardInterrupt:
            print("脚本被用户中断")
        finally:
            self.cleanup()


    def safe_move_and_click(self, x, y, click_delay=0.8, position_tolerance=5, max_retries=3, stability_check_time=0.2):
        """
        安全的鼠标移动和点击方法

        Args:
            x, y: 目标坐标
            click_delay: 点击后的延迟时间
            position_tolerance: 位置偏差容忍度（像素）
            max_retries: 最大重试次数
            stability_check_time: 位置稳定性检查时间

        Returns:
            bool: 操作是否成功
        """
        # 在执行点击操作前，确保游戏窗口置顶
        if hasattr(self, 'game_hwnd') and self.game_hwnd:
            try:
                # 将游戏窗口置顶并激活
                self.dm.SetWindowState(self.game_hwnd, 8)  # 8表示置顶
                self.dm.SetWindowState(self.game_hwnd, 1)  # 1表示激活
                time.sleep(0.1)  # 短暂等待窗口状态更新
            except Exception as e:
                print(f"⚠️ 窗口置顶失败: {e}")

        for attempt in range(max_retries):
            try:
                # 移动鼠标到目标位置
                self.dm.MoveTo(x, y)
                time.sleep(stability_check_time)

                # 获取当前鼠标位置进行验证
                try:
                    current_pos = self.dm.GetCursorPos()
                    if current_pos is not None:
                        # 大漠插件GetCursorPos返回格式：(状态码, x坐标, y坐标)
                        if isinstance(current_pos, (list, tuple)):
                            if len(current_pos) >= 3:
                                # 第一个元素是状态码，第二、三个元素是坐标
                                status, current_x, current_y = current_pos[0], current_pos[1], current_pos[2]
                                if status != 1:
                                    print(f"⚠️ 获取鼠标位置失败，状态码: {status}")
                                    raise ValueError("获取鼠标位置失败")
                            elif len(current_pos) >= 2:
                                # 兼容其他可能的格式
                                current_x, current_y = current_pos[0], current_pos[1]
                            else:
                                raise ValueError("位置数据格式不正确")
                        else:
                            # 如果返回的是单个值，可能需要其他方式解析
                            print(f"⚠️ 鼠标位置返回格式异常: {current_pos}")
                            raise ValueError("无法解析鼠标位置")

                        # 计算位置偏差
                        distance = ((current_x - x) ** 2 + (current_y - y) ** 2) ** 0.5

                        if distance <= position_tolerance:
                            # 位置正确，执行点击
                            self.dm.LeftClick()
                            time.sleep(click_delay)
                            print(f"✅ 成功点击坐标 ({x}, {y})")
                            return True
                        else:
                            print(f"⚠️ 位置偏差过大: 目标({x}, {y}), 实际({current_x}, {current_y}), 偏差{distance:.1f}像素")
                            if attempt < max_retries - 1:
                                print(f"🔄 重试第 {attempt + 2} 次...")
                                time.sleep(0.1)
                                continue
                    else:
                        print(f"⚠️ 无法获取当前鼠标位置，直接执行点击")
                        self.dm.LeftClick()
                        time.sleep(click_delay)
                        return True
                except Exception as pos_error:
                    print(f"⚠️ 获取鼠标位置时发生错误: {pos_error}，直接执行点击")
                    self.dm.LeftClick()
                    time.sleep(click_delay)
                    return True

            except Exception as e:
                print(f"❌ 点击操作异常: {str(e)}")
                if attempt < max_retries - 1:
                    print(f"🔄 重试第 {attempt + 2} 次...")
                    time.sleep(0.1)
                    continue
                else:
                    return False

        print(f"❌ 点击坐标 ({x}, {y}) 失败，已达到最大重试次数")
        return False

    def is_one_char_diff(self, s1, s2):
        """判断两个字符串是否只有一个字符不同"""
        # 如果两个字符串完全相同，直接返回True
        if s1 == s2:
            return True
        # 长度不同则直接排除
        if len(s1) != len(s2):
            return False
        # 统计不同字符的数量
        diff = 0
        for c1, c2 in zip(s1, s2):
            if c1 != c2:
                diff += 1
                # 提前终止：差异超过1次无需继续
                if diff > 1:
                    return False
        return diff == 1

    def keep_chinese(text):
        # 匹配所有中文字符（包括基本汉字和扩展区）
        pattern = re.compile(r'[\u4e00-\u9fff\u3400-\u4dbf\U00020000-\U0002a6df\U0002a700-\U0002ebef]')
        return ''.join(pattern.findall(text))

    def execute_after_guild1(self):
        """点击帮1之后的操作流程"""
        try:
            #点击输入框
            if not self.safe_move_and_click(497, 36, click_delay=1.0):
                raise Exception("点击搜索输入框失败")
            if not self.safe_move_and_click(378, 36, click_delay=1.0):
                raise Exception("再次点击搜索输入框失败")
            # 在搜索框中输入外观全称
            print(f"输入外观全称: {self.current_appearance}")
            self.dm.SendString(self.game_hwnd,self.current_appearance)
            time.sleep(1)
            
            # 获取需要发送的数量
            count = self.sendCount
            print(f"需要发送的数量: {count}")
            # 转换为int类型
            count = int(count)
            for i in range(count):
                print(f"开始查找第{i + 1}个")
                self.execute_find_appearance()

            if self.is_split:
                print("开始拆分")
                print("返回背包")
                if not self.safe_move_and_click(661, 40):
                    raise Exception("返回背包失败")

                print("点击搜索框")
                if not self.safe_move_and_click(1109, 81):
                    raise Exception("点击背包搜索框失败")

                print("输入礼盒名称")
                if not self.safe_move_and_click(985, 81):
                    raise Exception("点击礼盒名称输入框失败")
                self.dm.SendString(self.game_hwnd,self.gift_name)
                for i in range(count):
                    print(f"开始拆分第{i + 1}个")
                    self.execute_split()
            
            
            print("返回菜单")
            if not self.safe_move_and_click(1241, 34):
                raise Exception("返回菜单失败")
            self.execute_friend_and_mail()

            if self.is_split:
                print("拆分剩下的物品，放回帮会仓库！")
                self.execute_split_remain()

        except Exception as e:
            raise Exception(f"帮1后续操作失败: {str(e)}")
        
    def execute_split_remain(self):
        """拆分剩下的物品，放回帮会仓库"""
        try:
            count = int(self.sendCount)  # 转换为int类型
            other_items = self.other_items
            if other_items == "":
                print("没有输入其他散件名称，跳过存储剩余外观")
                return
            #逗号分割other_items
            other_items_list = other_items.split(",")
            for item in other_items_list:
                for i in range(5):
                    # 点击esc
                    print("点击esc...")
                    self.dm.KeyPress(27)
                    time.sleep(0.5)

                    self.dm.Capture(1222,688,1256,705,"logout.png")
                    time.sleep(0.5)
                    print("正在识别图片...")
                    with open("logout.png", 'rb') as f:
                        result1 = self.ocr.classification(f.read())
                        print(f"识别结果: {result1}")
                        if '退出' in result1:
                            # 点击esc
                            print("点击esc...")
                            self.dm.KeyPress(27)
                            time.sleep(0.5)
                            break
                # 点击背包按钮
                print("点击背包按钮成功")
                if not self.safe_move_and_click(1051, 39, click_delay=1.0):
                    raise Exception("点击背包按钮失败")

                # 点击仓库按钮
                print("点击仓库按钮成功")
                if not self.safe_move_and_click(836, 671, click_delay=1.0):
                    raise Exception("点击仓库按钮失败")

                # 点击帮会按钮
                print("点击帮会按钮成功")
                if not self.safe_move_and_click(39, 281, click_delay=1.0):
                    raise Exception("点击帮会按钮失败")

                print(f"开始查找{item}")
                print("点击搜索框")
                if not self.safe_move_and_click(1109, 81):
                    raise Exception("点击搜索框失败")
                
                if not self.safe_move_and_click(971, 81):
                    raise Exception("点击物品名称输入框失败")
                
                print("输入物品名称")
                self.dm.SendString(self.game_hwnd,item)

                if not self.safe_move_and_click(856, 164):
                    raise Exception("点击物品失败")

                if not self.safe_move_and_click(856, 164):
                    raise Exception("再次点击物品失败")

                print("找图并点击存储")
                result = self.find_pic_retry(
                    710,400,864,673,
                    "images/单个存储按钮.bmp|images/多个存储按钮.bmp|images/单个存储按钮1.bmp",
                    "202020",
                    0.9
                )
                if result[0] != -1:
                    for i in range(count):
                        
                        for j in range(6):
                            flag = False
                            print(f"点击仓库第{j + 1}页")
                            if not self.safe_move_and_click(134, 90 + (j) *64):
                                print(f"点击仓库第{j + 1}页失败")
                                continue

                            if not self.safe_move_and_click(856, 164):
                                print("点击物品失败")
                                continue

                            if not self.safe_move_and_click(856, 164):
                                print("再次点击物品失败")
                                continue

                            if not self.safe_move_and_click(result[1], result[2]):
                                print("点击存储按钮失败")
                                continue

                            print("判断仓库格子是否已满")
                            self.dm.Capture(585,190,691,214,"判断是否已满.png")
                            time.sleep(0.5)
                            print("正在识别外观名称（使用Umi-OCR）...")
                            with open("判断是否已满.png", 'rb') as f:
                                # 优先使用Umi-OCR识别外观名称，提高精度
                                if self.umi_ocr:
                                    try:
                                        result1 = self.umi_ocr.classification(f.read())
                                        print(f"Umi-OCR识别结果: {result1}")
                                    except Exception as e:
                                        print(f"Umi-OCR识别失败，使用ddddocr备用: {e}")
                                        f.seek(0)  # 重置文件指针
                                        result1 = self.ocr.classification(f.read())
                                        print(f"ddddocr识别结果: {result1}")
                                else:
                                    result1 = self.ocr.classification(f.read())
                                    print(f"ddddocr识别结果: {result1}")
                                if "已满" in result1:
                                    flag = True
                                    print("仓库当前页格子已满，进入下一页") 
                                else:
                                    break #退出循环
                        if flag:
                            print("仓库已满，退出循环")
                            raise Exception("仓库已满")              
        except Exception as e:
            raise Exception(f"拆分剩下的物品操作失败: {str(e)}")

    def execute_split(self):
        """执行拆分操作"""
        try:
            if not self.safe_move_and_click(856, 164):
                raise Exception("点击礼盒失败")

            if not self.safe_move_and_click(856, 164):
                raise Exception("再次点击礼盒失败")

            print("找图并点击使用")
            result = self.find_pic_retry(
                732,533,858,655,
                "images/使用.bmp",
                "202020",
                0.9
            )
            if result[0] != -1:
                if not self.safe_move_and_click(result[1], result[2]):
                    raise Exception("点击使用按钮失败")
            else:
                raise Exception(f"未找到使用按钮,拆分盒子失败，现在盒子在账号{self.account}背包里")
            print("等待6秒")
            time.sleep(6)
        except Exception as e:
            raise Exception(f"拆分操作失败: {str(e)}")

    def execute_find_appearance(self):
        """执行查找外观操作"""
        try:
            for i in range(6):
                print(f"点击第{i + 1}页")
                if not self.safe_move_and_click(134, 90 + (i) *64):
                    print(f"点击第{i + 1}页失败")
                    continue

                # 添加一个标志变量
                should_break = False
                appearance_name =self.current_appearance.replace("·","")
                # 判断是否是拆分
                if self.is_split:
                    appearance_name = self.gift_name.replace("·","")

                appearance_name = appearance_name.replace("（","")
                appearance_name = appearance_name.replace("）","")
                # pattern = re.compile(r'[\u4e00-\u9fff\u3400-\u4dbf\U00020000-\U0002a6df\U0002a700-\U0002ebef]')
                # appearance_name = ''.join(pattern.findall(self.current_appearance))
                print(f"待查找外观：{appearance_name}")

                for i1 in range(5):
                    for i2 in range(5):

                        print(f"点击第{i1 + 1}行，第{i2 + 1}格:{212 + (i2) * 72},{105 + (i1) * 72}")
                        if not self.safe_move_and_click(204 + (i2) * 72, 101 + (i1) * 72):
                            print(f"点击第{i1 + 1}行，第{i2 + 1}格失败")
                            continue

                        print("再点一次")
                        if not self.safe_move_and_click(204 + (i2) * 72, 101 + (i1) * 72):
                            print(f"再次点击第{i1 + 1}行，第{i2 + 1}格失败")
                            continue

                        flag = True
                        print(f"查找点击取出按钮")
                        result = self.find_pic_retry(
                            580,510,1003,698,
                            "images/单个取出按钮.bmp|images/多个取出按钮.bmp|images/单个取出按钮1.bmp",
                            "202020",
                            0.9
                        )
                        time.sleep(0.5)
                        if result[0] != -1:
                            flag = False
                            print("找到取出按钮")
                            self.dm.Capture(527,88,787,135,"外观名称.png")
                            time.sleep(0.5)
                            print("正在识别外观名称（使用Umi-OCR）...")
                            with open("外观名称.png", 'rb') as f:
                                # 优先使用Umi-OCR识别外观名称，提高精度
                                if self.umi_ocr:
                                    try:
                                        result1 = self.umi_ocr.classification(f.read())
                                        print(f"Umi-OCR识别结果: {result1}")
                                    except Exception as e:
                                        print(f"Umi-OCR识别失败，使用ddddocr备用: {e}")
                                        f.seek(0)  # 重置文件指针
                                        result1 = self.ocr.classification(f.read())
                                        print(f"ddddocr识别结果: {result1}")
                                else:
                                    result1 = self.ocr.classification(f.read())
                                    print(f"ddddocr识别结果: {result1}")

                                # 清理识别结果
                                result1 = result1.replace("·","")
                                result1 = result1.replace("（","")
                                result1 = result1.replace("）","")
                                print(f"清理后的识别结果: {result1}")
                                
                                if self.is_one_char_diff(appearance_name, result1):
                                    print(f"找到{appearance_name}")
                                    if result[0] == 1:
                                        for i in range(20):
                                            self.safe_move_and_click(556, 555, click_delay=0.3)
                                    if self.safe_move_and_click(result[1], result[2]):
                                        return True
                                    else:
                                        print("点击取出按钮失败")
                                else:
                                    print("Umi-OCR识别失败，尝试使用ddddocr")
                                    with open("外观名称.png", 'rb') as f:
                                        result1 = self.ocr.classification(f.read())
                                    result1 = result1.replace("·","").replace("（","").replace("）","")
                                    print(f"ddddocr识别结果: {result1}")
                                    if self.is_one_char_diff(appearance_name, result1):
                                        print(f"找到{appearance_name}")
                                        if result[0] == 1:
                                            for i in range(20):
                                                self.safe_move_and_click(556, 555, click_delay=0.3)
                                        if self.safe_move_and_click(result[1], result[2]):
                                            return True
                                        else:
                                            print("点击取出按钮失败")
                                    else:
                                        print("ddddocr识别失败，尝试使用通义千问VL-OCR")
                                        try:
                                            # 请在此处填入您的通义千问API-KEY,并确保已安装dashscope依赖
                                            dashscope.api_key = "sk-dd69ee0912b74553a256f026c2ec9018"
                                            local_file_path = 'file://外观名称.png'
                                            rsp = dashscope.MultiModalConversation.call(
                                                model='qwen-vl-ocr',
                                                messages=[{
                                                    'role': 'user',
                                                    'content': [
                                                        {'image': local_file_path},
                                                        {'text': '图片中的文字是什么'}
                                                    ]
                                                }]
                                            )
                                            if rsp.status_code == HTTPStatus.OK:
                                                result1 = rsp.output.choices[0].message.content[0]['text']
                                                result1 = result1.replace("·","").replace("（","").replace("）","")
                                                print(f"通义千问识别结果: {result1}")
                                                if self.is_one_char_diff(appearance_name, result1):
                                                    print(f"找到{appearance_name}")
                                                    if result[0] == 1:
                                                        for i in range(20):
                                                            self.safe_move_and_click(556, 555, click_delay=0.3)
                                                    if self.safe_move_and_click(result[1], result[2]):
                                                        return True
                                                    else:
                                                        print("点击取出按钮失败")
                                            else:
                                                print(f"通义千问VL-OCR调用失败: {rsp.message}")
                                        except Exception as e:
                                            print(f"调用通义千问VL-OCR时发生错误: {e}")
                        if flag:
                            print("当前页无物品，去往下一页")
                            should_break = True
                            break  # 退出i2循环        

                    if should_break:
                        break  # 退出i1循环
            raise Exception("未找到物品！")
        except Exception as e:
            raise Exception(f"查找外观失败: {str(e)}")

    def execute_friend_and_mail(self):
        """执行发信操作"""
        try: 
            for i in range(5):
                # 点击esc
                print("点击esc...")
                self.dm.KeyPress(27)
                time.sleep(0.5)

                self.dm.Capture(1222,688,1256,705,"logout.png")
                time.sleep(0.5)
                print("正在识别图片...")
                with open("logout.png", 'rb') as f:
                    result1 = self.ocr.classification(f.read())
                    print(f"识别结果: {result1}")
                    if '退出' in result1:
                        break
            print(f"点击邮件")
            if not self.safe_move_and_click(1242, 200):
                raise Exception("点击邮件失败")

            print("点击寄信")
            if not self.safe_move_and_click(93, 654):
                raise Exception("点击寄信失败")

            print("点击寄信查找标签...")
            if not self.safe_move_and_click(262, 178):
                raise Exception("点击寄信查找标签失败")

            print("点击收件人...")
            if not self.safe_move_and_click(793, 115):
                raise Exception("点击收件人失败")


             # 输入好友昵称
            print(f"输入好友昵称: {self.character_name}")
            self.dm.SendString(self.game_hwnd,self.character_name)
            time.sleep(0.5)

            print("点击信件主题...")
            if not self.safe_move_and_click(782, 190):
                raise Exception("点击信件主题失败")

            # 输入信件主题
            print(f"输入信件主题: {self.current_appearance}")
            self.dm.SendString(self.game_hwnd,self.current_appearance)
            time.sleep(0.5)

            print("点击添加物品按钮")
            if not self.safe_move_and_click(538, 531):
                raise Exception("点击添加物品按钮失败")

            print("点击背包搜索框")
            if not self.safe_move_and_click(377, 24):
                raise Exception("点击背包搜索框失败")

            if not self.safe_move_and_click(233, 28):
                raise Exception("点击搜索输入框失败")
            self.dm.SendString(self.game_hwnd,self.current_appearance)

            count = int(self.sendCount)
            for i in range(count):
                print("点击搜索到的外观")
                if not self.safe_move_and_click(125 + i*70, 113):
                    print(f"点击第{i+1}个外观失败")
                    continue

                if not self.safe_move_and_click(125 + i*70, 113):
                    print(f"再次点击第{i+1}个外观失败")
                    continue

                for j in range(5):
                    if not self.safe_move_and_click(693, 541,click_delay=0.2):
                        print(f"点击添加第{i+1}个外观到邮件失败")
                        break

                if not self.safe_move_and_click(534, 593):
                    print(f"点击添加第{i+1}个外观到邮件失败")
                
            # 截图并保存到png目录下，命名规则为：日期时间-外观全称-区服-帮会-角色名.png
            print("截图并保存到png目录下...")
            self.image_path1 = f"png/{datetime.now().strftime('%Y%m%d_%H%M%S')}-{self.current_appearance}-{self.region}-{self.current_guild}-{self.character_name}.png"
            self.dm.CaptureJpg(0,0,2000,2000, self.image_path1,20) 
            time.sleep(1)

            print("点击发送按钮")
            if not self.safe_move_and_click(830, 655):
                raise Exception("点击发送按钮失败")
            if not self.safe_move_and_click(830, 655):
                raise Exception("再次点击发送按钮失败")
            time.sleep(1)

            print("点击确定发送")
            if not self.safe_move_and_click(746, 540, click_delay=1.0):
                raise Exception("点击确定发送失败")

            print("截图并保存到png目录下...")
            self.image_path2 = f"png/{datetime.now().strftime('%Y%m%d_%H%M%S')}-{self.current_appearance}-{self.region}-{self.current_guild}-{self.character_name}.png"
            self.dm.CaptureJpg(0,0,2000,2000, self.image_path2,20)
            time.sleep(1)

            #发送消息到千牛客服端
            # self.send_qianniu_delivery_message()


            print("点击ESC两次")
            self.dm.KeyPress(27)
            time.sleep(0.5)
            self.dm.KeyPress(27)
            time.sleep(0.5)

            

            return True
        except Exception as e:
            print(f"好友添加和发信操作失败: {str(e)}")
            return False

    def load_account_config(self):
        """加载账号配置"""
        try:
            config_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'account_config.json')
            if os.path.exists(config_path):
                with open(config_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            print(f"加载配置文件失败: {str(e)}")
        return {f"帮{i}": {"account": "", "password": ""} for i in range(1, 12)}

    def update_order_status(self, order_id, character_name=None, image_path=[], is_success=True, error_message=None):
        """更新订单状态"""
        try:
            # 连接MySQL数据库
            conn = mysql.connector.connect(**MYSQL_CONFIG)
            cursor = conn.cursor()

            if is_success:
                # 读取图片并转换为base64
                image_base64 = ''
                for path in image_path:
                    if path and os.path.exists(path):
                        with open(path, 'rb') as img_file:
                            file_content = img_file.read()
                        # 构造Multipart数据，指定boundary
                        encoder = MultipartEncoder(
                            fields={
                                'file': (path[4:], file_content, 'image/png')
                            },
                        )
                        # 发送POST请求
                        response = requests.post(
                            'http://wblxt.bingtangzhanghao.cn:8084/common/upload',
                            data=encoder,
                            headers={'Content-Type': encoder.content_type}  # 关键修复点：设置正确的Content-Type
                        )
                        print(f"上传相应内容：{response.text}")
                        # 获取上传返回的路径
                        response_data = response.json()
                        if response_data.get('code') == 200:
                            # 直接获取fileName字段
                            uploaded_path = response_data.get('fileName')
                            
                            if uploaded_path:
                                if image_base64:  # 非首次，添加逗号
                                    image_base64 = image_base64 + "," + uploaded_path
                                else:  # 首次，直接赋值
                                    image_base64 = uploaded_path
                                print(f"✅ 文件上传成功！服务器存储路径: {uploaded_path}")
                                print(f"   原始文件名: {response_data.get('originalFilename')}")
                                print(f"   访问URL: {response_data.get('url')}")
                            else:
                                print("⚠️ 警告：服务器返回空路径")
                        else:
                            print(f"❌ 上传失败：{response_data.get('msg', '未知业务错误')}")

                # 更新订单信息
                sql = """UPDATE game_order 
                        SET character_name = %s, image = %s ,field3='99'
                        WHERE id = %s"""
                cursor.execute(sql, (character_name, image_base64, order_id))
            else:
                # 更新失败状态
                sql = """UPDATE game_order 
                        SET  field4 = %s 
                        WHERE id = %s"""
                cursor.execute(sql, (error_message, order_id))
                print(f"更新订单 {order_id} 失败状态: {error_message}")


            conn.commit()

            # 如果更新成功，发送MQ通知
            if is_success:
                self.send_delivery_notification(order_id)

        except Exception as e:
            print(f"更新订单状态失败: {str(e)}")

        finally:
            if 'cursor' in locals():
                cursor.close()
            if 'conn' in locals():
                conn.close()

    def send_delivery_notification(self, order_id):
        """发送出库完成通知到Java端"""
        try:
            print(f"发送出库通知: 订单{order_id}")

            # 创建出库通知发送器
            notification_sender = DeliveryNotificationSender()

            if not notification_sender.connect():
                print("❌ 出库通知MQ连接失败")
                return False

            # 获取订单信息以确定是否拆分
            is_split = hasattr(self, 'is_split') and self.is_split
            split_name = getattr(self, 'gift_name', None) if is_split else None
            warehouse = getattr(self, 'current_guild', "帮1")

            # 发送通知
            success = notification_sender.send_delivery_notification(
                order_id=order_id,
                is_split=is_split,
                split_name=split_name,
                warehouse=warehouse
            )

            notification_sender.close()

            if success:
                print(f"✅ 出库通知发送成功: 订单{order_id}")
            else:
                print(f"❌ 出库通知发送失败: 订单{order_id}")

            return success

        except Exception as e:
            print(f"❌ 发送出库通知异常: {str(e)}")
            import traceback
            traceback.print_exc()
            return False

    def send_to_java(self, game_order):
        """发送消息到Java端的更新订单队列"""
        try:
            # 建立RabbitMQ连接
            credentials = pika.PlainCredentials(
                RABBITMQ_CONFIG['username'],
                RABBITMQ_CONFIG['password']
            )
            parameters = pika.ConnectionParameters(
                host=RABBITMQ_CONFIG['host'],
                port=RABBITMQ_CONFIG['port'],
                virtual_host=RABBITMQ_CONFIG['virtual_host'],
                credentials=credentials
            )
            connection = pika.BlockingConnection(parameters)
            channel = connection.channel()

            # 声明交换机
            channel.exchange_declare(
                exchange='OrderExchange',
                exchange_type='direct',
                durable=True

            )

            # 声明队列
            channel.queue_declare(queue='OrderQueue', durable=True)

            # 绑定队列到交换机
            channel.queue_bind(
                exchange='OrderExchange',
                queue='OrderQueue',
                routing_key='OrderRouting'
            )

            # 发送消息
            channel.basic_publish(
                exchange='OrderExchange',
                routing_key='OrderRouting',
                body=json.dumps(game_order),
                properties=pika.BasicProperties(
                    delivery_mode=2,  # 使消息持久化
                    content_type='application/json'
                )
            )

            print(f"已发送更新订单消息到Java端: {game_order}")
            connection.close()
        except Exception as e:
            print(f"发送消息到Java端失败: {str(e)}")
            raise

    def send_qianniu_delivery_message(self):
        """
        发送千牛发货消息
        """
        try:
            # 检查是否有买家旺旺信息
            if not hasattr(self, 'buyer_wangwang') or not self.buyer_wangwang:
                print("⚠️  买家旺旺信息为空，跳过千牛消息发送")
                return False

            print(f"正在发送千牛通知消息给买家: {self.buyer_wangwang}")

            # 获取截图路径
            screenshot_path = self.image_path1 + "," + self.image_path2

            # 发送千牛通知消息（固定文案+截图）
            success = send_qianniu_notification(
                buyer_nick=self.buyer_wangwang,
                screenshot_path=screenshot_path,
                buyer_uid=getattr(self, 'buyer_uid', ''),
                user_nick=getattr(self, 'user_nick', None)  # 传递客服名称
            )

            if success:
                print(f"✅ 千牛通知消息发送成功: {self.buyer_wangwang}")

                # 如果填写了淘宝订单，调用千牛发货订单接口
                if hasattr(self, 'taobao_id') and self.taobao_id:
                    print(f"检测到淘宝订单号: {self.taobao_id}，准备调用发货接口")
                    shipment_success = self.send_qianniu_shipment_notification()
                    if shipment_success:
                        print(f"✅ 千牛发货订单接口调用成功: {self.taobao_id}")
                    else:
                        print(f"❌ 千牛发货订单接口调用失败: {self.taobao_id}")
                else:
                    print("⚠️ 未填写淘宝订单号，跳过发货接口调用")
            else:
                print(f"❌ 千牛通知消息发送失败: {self.buyer_wangwang}")

            return success

        except Exception as e:
            print(f"❌ 千牛消息发送异常: {str(e)}")
            return False

    def send_qianniu_shipment_notification(self):
        """
        调用千牛发货订单接口
        """
        try:
            # 检查必要信息
            if not hasattr(self, 'taobao_id') or not self.taobao_id:
                print("⚠️ 淘宝订单号为空，无法调用发货接口")
                return False

            if not hasattr(self, 'user_nick') or not self.user_nick:
                print("⚠️ 客服名称为空，无法调用发货接口")
                return False

            print(f"正在调用千牛发货订单接口: 订单={self.taobao_id}, 客服={self.user_nick}")

            # 使用便捷函数调用发货接口
            from qianniu_client import send_qianniu_order_shipment
            success = send_qianniu_order_shipment(
                order_id=self.taobao_id,
                user_nick=self.user_nick
            )

            return success

        except Exception as e:
            print(f"❌ 千牛发货接口调用异常: {str(e)}")
            import traceback
            traceback.print_exc()
            return False

if __name__ == "__main__":

    # 启动脚本
    script = GameScript()
    script.script_main()
    
            
            