#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
检查相关库的版本信息
"""

def check_versions():
    """检查相关库版本"""
    print("=" * 50)
    print("检查相关库版本信息")
    print("=" * 50)
    
    libraries = [
        'onnxruntime',
        'ddddocr', 
        'opencv-python',
        'pillow',
        'numpy'
    ]
    
    for lib in libraries:
        try:
            if lib == 'opencv-python':
                import cv2
                version = cv2.__version__
                print(f"✅ {lib}: {version}")
            elif lib == 'pillow':
                import PIL
                version = PIL.__version__
                print(f"✅ {lib}: {version}")
            else:
                module = __import__(lib)
                version = getattr(module, '__version__', '未知版本')
                print(f"✅ {lib}: {version}")
        except ImportError as e:
            print(f"❌ {lib}: 未安装 ({str(e)})")
        except Exception as e:
            print(f"⚠️ {lib}: 检查失败 ({str(e)})")
    
    print("\n" + "=" * 50)
    print("系统信息")
    print("=" * 50)
    
    import sys
    import platform
    
    print(f"Python版本: {sys.version}")
    print(f"操作系统: {platform.system()} {platform.release()}")
    print(f"架构: {platform.architecture()[0]}")

if __name__ == "__main__":
    check_versions()
