#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
无OCR启动脚本
当onnxruntime有问题时，可以使用此脚本启动程序
"""

import sys
import os

def check_dependencies():
    """检查依赖项"""
    print("检查依赖项...")
    
    missing_deps = []
    
    # 检查关键依赖
    try:
        import tkinter
        print("✅ tkinter 可用")
    except ImportError:
        missing_deps.append("tkinter")
        print("❌ tkinter 不可用")
    
    try:
        import pika
        print("✅ pika 可用")
    except ImportError:
        missing_deps.append("pika")
        print("❌ pika 不可用")
    
    try:
        import requests
        print("✅ requests 可用")
    except ImportError:
        missing_deps.append("requests")
        print("❌ requests 不可用")
    
    # 检查OCR相关
    try:
        import ddddocr
        print("✅ ddddocr 可用")
    except ImportError as e:
        print(f"⚠️ ddddocr 不可用: {str(e)}")
        print("⚠️ 程序将在无OCR模式下运行")
    
    if missing_deps:
        print(f"\n❌ 缺少关键依赖: {', '.join(missing_deps)}")
        print("请安装缺少的依赖项后重试")
        return False
    
    return True

def start_gui():
    """启动GUI"""
    try:
        print("启动GUI...")
        import gui
        gui.main()
    except Exception as e:
        print(f"❌ GUI启动失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

def main():
    """主函数"""
    print("=" * 60)
    print("发货系统 - 无OCR模式启动")
    print("=" * 60)
    
    print("⚠️ 注意事项:")
    print("1. 此模式下OCR功能将受限")
    print("2. 外观识别可能不准确")
    print("3. 建议修复onnxruntime问题后使用正常模式")
    print()
    
    # 检查Python版本
    if sys.maxsize > 2**32:
        print("❌ 错误：必须使用32位Python版本！")
        print("当前使用的是64位Python")
        input("按回车键退出...")
        return
    
    print(f"✅ Python版本: {sys.version}")
    print(f"✅ 架构: 32位")
    print()
    
    # 检查依赖
    if not check_dependencies():
        input("按回车键退出...")
        return
    
    print("\n" + "=" * 60)
    print("启动程序")
    print("=" * 60)
    
    # 启动GUI
    if start_gui():
        print("✅ 程序启动成功")
    else:
        print("❌ 程序启动失败")
        input("按回车键退出...")

if __name__ == "__main__":
    try:
        main()
    except KeyboardInterrupt:
        print("\n程序被用户中断")
    except Exception as e:
        print(f"\n程序异常: {str(e)}")
        import traceback
        traceback.print_exc()
        input("按回车键退出...")
