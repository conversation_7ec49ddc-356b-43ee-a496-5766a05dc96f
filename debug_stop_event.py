#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
调试stop_event问题
"""

import sys
import os
import threading

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def debug_gamescript_creation():
    """调试GameScript创建过程"""
    print("=" * 60)
    print("调试GameScript创建过程")
    print("=" * 60)
    
    try:
        from dm_main import GameScript
        
        print("1. 创建GameScript实例（无参数）...")
        script = GameScript()
        
        print("2. 检查属性...")
        print(f"   hasattr(script, 'stop_event'): {hasattr(script, 'stop_event')}")
        
        if hasattr(script, 'stop_event'):
            print(f"   type(script.stop_event): {type(script.stop_event)}")
            print(f"   script.stop_event is None: {script.stop_event is None}")
            
            if script.stop_event is not None:
                print(f"   script.stop_event.is_set(): {script.stop_event.is_set()}")
            
            print("3. 测试check_stop方法...")
            try:
                result = script.check_stop()
                print(f"   check_stop()结果: {result}")
            except Exception as e:
                print(f"   check_stop()失败: {str(e)}")
                return False
        else:
            print("   ❌ stop_event属性不存在！")
            return False
        
        print("4. 模拟消息处理...")
        # 创建一个模拟消息对象
        class MockMessage:
            def __init__(self):
                self.order_id = 999999
                self.account = "test_account"
                self.region = "test_region"
                self.guild = "帮1"
                self.appearance = "test_appearance"
                self.sendCount = 1
                self.character_name = "test_character"
                self.is_split = False
                self.gift_name = None
                self.gift_color = None
                self.other_items = None
                self.buyer_wangwang = None
                self.user_nick = None
                self.taobao_id = None
        
        mock_message = MockMessage()
        
        print("5. 测试handle_game_message方法的开始部分...")
        try:
            # 只测试方法的开始部分，不执行实际的游戏操作
            print(f"   🎯 开始处理游戏消息: {mock_message.order_id}")
            
            # 模拟设置属性
            script.order_id = mock_message.order_id
            script.account = mock_message.account
            script.region = mock_message.region
            script.guild = mock_message.guild
            script.appearance = mock_message.appearance
            script.sendCount = mock_message.sendCount
            script.character_name = mock_message.character_name
            
            print("   ✅ 属性设置成功")
            
            # 再次测试check_stop
            result = script.check_stop()
            print(f"   check_stop()结果: {result}")
            
            return True
            
        except AttributeError as ae:
            if "stop_event" in str(ae):
                print(f"   ❌ stop_event属性错误: {str(ae)}")
                
                # 详细检查属性
                print("   详细属性检查:")
                print(f"     script.__dict__.keys(): {list(script.__dict__.keys())}")
                
                if 'stop_event' in script.__dict__:
                    print(f"     script.__dict__['stop_event']: {script.__dict__['stop_event']}")
                else:
                    print("     stop_event不在__dict__中")
                
                return False
            else:
                print(f"   ⚠️ 其他属性错误: {str(ae)}")
                return True
        
    except Exception as e:
        print(f"❌ 调试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """主函数"""
    print("stop_event问题调试")
    print("详细分析GameScript的stop_event属性问题")
    print()
    
    try:
        result = debug_gamescript_creation()
        
        print("\n" + "=" * 60)
        print("调试结果")
        print("=" * 60)
        
        if result:
            print("✅ GameScript的stop_event属性工作正常")
            print("问题可能在其他地方，需要进一步调查")
        else:
            print("❌ 确认存在stop_event属性问题")
            print("需要检查GameScript的初始化过程")
        
    except Exception as e:
        print(f"调试过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
