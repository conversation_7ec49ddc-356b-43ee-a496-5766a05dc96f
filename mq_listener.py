import json
import pika
from config import RABBITMQ_CONFIG
import struct
import traceback
import datetime

class GameMessage:
    def __init__(self, account, password, region, guild, appearance, character_name, order_id, is_split, gift_name, gift_color, other_items, sendCount, buyer_wangwang, user_nick=None, taobao_id=None, restart=False):
        self.account = account
        self.password = password
        self.region = region
        self.guild = guild
        self.appearance = appearance
        self.character_name = character_name
        self.order_id = order_id
        self.is_split = is_split
        self.gift_name = gift_name
        self.gift_color = gift_color
        self.other_items = other_items
        self.sendCount = sendCount
        self.buyer_wangwang = buyer_wangwang
        self.user_nick = user_nick
        self.taobao_id = taobao_id
        self.restart = restart

class MQListener:
    def __init__(self, game_script):
        self.connection = None
        self.channel = None
        self.game_script = game_script  # 保存GameScript实例
        self.current_channel = None  # 保存当前消息的通道
        self.current_method = None   # 保存当前消息的方法信息

    def connect(self):
        try:
            print("正在连接RabbitMQ服务器...")
            print(f"连接信息: {RABBITMQ_CONFIG}")
            
            credentials = pika.PlainCredentials(
                RABBITMQ_CONFIG['username'],
                RABBITMQ_CONFIG['password']
            )
            parameters = pika.ConnectionParameters(
                host=RABBITMQ_CONFIG['host'],
                port=RABBITMQ_CONFIG['port'],
                virtual_host=RABBITMQ_CONFIG['virtual_host'],
                credentials=credentials,
                heartbeat=60  # 添加60秒的心跳超时
            )
            self.connection = pika.BlockingConnection(parameters)
            self.channel = self.connection.channel()
            
            print("声明交换机和队列，并进行绑定...")
            self.channel.exchange_declare(exchange=RABBITMQ_CONFIG['exchange'], exchange_type='direct',durable=True)
            self.channel.queue_declare(queue=RABBITMQ_CONFIG['queue_name'], durable=True)
            self.channel.queue_bind(
                exchange=RABBITMQ_CONFIG['exchange'],
                queue=RABBITMQ_CONFIG['queue_name'],
                routing_key=RABBITMQ_CONFIG['routing_key']
            )

            # 设置每个消费者一次只处理一条消息
            self.channel.basic_qos(prefetch_count=1)
            
            print("RabbitMQ连接成功")
            return True
        except Exception as e:
            print(f"连接过程中发生错误: {str(e)}")
            return False

    def start_listening(self, callback):
        if not self.channel:  # 检查通道是否存在
            print("未成功连接RabbitMQ，无法开始监听")
            return

        def message_handler(ch, method, properties, body):
            try:
                # 保存当前消息的通道和方法信息，用于后续确认
                self.current_channel = ch
                self.current_method = method

                # 直接解析 JSON 消息
                message = json.loads(body.decode('utf-8'))
                print(f"收到消息: {message}")

                # 从配置中获取对应帮会的账号密码
                guild = message.get('guild')

                # 安全地获取账号配置
                if hasattr(self.game_script, 'account_config') and self.game_script.account_config:
                    account_info = self.game_script.account_config.get(guild, {})
                else:
                    # 如果没有account_config属性，尝试加载配置
                    try:
                        account_config = self.game_script.load_account_config()
                        account_info = account_config.get(guild, {})
                    except Exception as e:
                        print(f"加载账号配置失败: {str(e)}")
                        account_info = {}

                # 创建 GameMessage 对象
                game_message = GameMessage(
                    account=account_info.get('account'),
                    password=account_info.get('password'),
                    region=message.get('region'),
                    guild=message.get('guild'),
                    appearance=message.get('appearance', ''),
                    character_name=message.get('character_name'),
                    order_id=message.get('order_id'),
                    is_split=message.get('is_split', False),
                    gift_name=message.get('gift_name', ''),
                    gift_color=message.get('gift_color', ''),
                    other_items=message.get('other_items', ''),
                    sendCount=message.get('sendCount', 0),
                    buyer_wangwang=message.get('buyer_wangwang', ''),
                    restart=message.get('restart', False),
                )

                # 使用GameScript实例设置server_name和guild_name
                self.game_script.server_name = game_message.region
                self.game_script.guild_name = game_message.guild

                # 保存channel和delivery_tag到GameScript实例
                self.game_script.mq_channel = ch
                self.game_script.mq_delivery_tag = method.delivery_tag

                callback(game_message)

                # 消息处理成功，确认消息
                # 移除立即确认的代码
                # ch.basic_ack(delivery_tag=method.delivery_tag)
                print("等待业务流程完成后再确认消息")


            except Exception as e:
                print(f"消息处理失败: {str(e)}")
                print(traceback.format_exc())
                # 消息处理失败时，将消息重新放回队列
                ch.basic_nack(delivery_tag=method.delivery_tag, requeue=True)

        print(f"开始监听队列: {RABBITMQ_CONFIG['queue_name']}")
        self.channel.basic_consume(
            queue=RABBITMQ_CONFIG['queue_name'],
            on_message_callback=message_handler,
            auto_ack=False
        )
        self.channel.start_consuming()

    def ack_current_message(self):
        """确认当前正在处理的消息"""
        try:
            if self.current_channel and self.current_method:
                self.current_channel.basic_ack(delivery_tag=self.current_method.delivery_tag)
                print(f"✅ 已确认消息: {self.current_method.delivery_tag}")

                # 清空当前消息信息
                self.current_channel = None
                self.current_method = None
                return True
            else:
                print("⚠️ 没有待确认的消息")
                return False
        except Exception as e:
            print(f"❌ 确认消息失败: {str(e)}")
            return False

    def nack_current_message(self, requeue=True):
        """拒绝当前正在处理的消息"""
        try:
            if self.current_channel and self.current_method:
                self.current_channel.basic_nack(
                    delivery_tag=self.current_method.delivery_tag,
                    requeue=requeue
                )
                print(f"✅ 已拒绝消息: {self.current_method.delivery_tag}, 重新排队: {requeue}")

                # 清空当前消息信息
                self.current_channel = None
                self.current_method = None
                return True
            else:
                print("⚠️ 没有待处理的消息")
                return False
        except Exception as e:
            print(f"❌ 拒绝消息失败: {str(e)}")
            return False

    def consume_all_messages(self):
        """消费队列中的所有消息"""
        try:
            if not self.channel:
                print("❌ MQ连接不可用，无法消费消息")
                return False

            print("🔄 开始消费队列中的所有消息...")

            # 获取队列信息
            queue_name = RABBITMQ_CONFIG['queue_name']
            method_frame, header_frame, body = self.channel.basic_get(queue=queue_name, auto_ack=False)

            consumed_count = 0

            # 循环获取并确认所有消息
            while method_frame:
                try:
                    # 解析消息内容（用于日志显示）
                    message = json.loads(body.decode('utf-8'))
                    order_id = message.get('order_id', 'Unknown')
                    appearance = message.get('appearance', 'Unknown')

                    print(f"📤 消费消息: 订单={order_id}, 外观={appearance}")

                    # 确认消息
                    self.channel.basic_ack(delivery_tag=method_frame.delivery_tag)
                    consumed_count += 1

                    # 获取下一条消息
                    method_frame, header_frame, body = self.channel.basic_get(queue=queue_name, auto_ack=False)

                except Exception as e:
                    print(f"⚠️ 处理消息时出错: {str(e)}")
                    # 即使解析失败，也确认消息以避免重复处理
                    self.channel.basic_ack(delivery_tag=method_frame.delivery_tag)
                    consumed_count += 1

                    # 继续获取下一条消息
                    method_frame, header_frame, body = self.channel.basic_get(queue=queue_name, auto_ack=False)

            if consumed_count > 0:
                print(f"✅ 成功消费了 {consumed_count} 条消息")
            else:
                print("ℹ️ 队列中没有待消费的消息")

            # 清空当前消息信息
            self.current_channel = None
            self.current_method = None

            return True

        except Exception as e:
            print(f"❌ 消费所有消息失败: {str(e)}")
            import traceback
            traceback.print_exc()
            return False

    def close(self):
        if self.connection:
            self.connection.close()

    def is_connected(self):
        """检查MQ连接状态"""
        try:
            if not self.connection or not self.connection.is_open:
                return False
            if not self.channel or not self.channel.is_open:
                return False
            # 尝试进行一个无害的操作来验证连接
            self.channel.basic_qos(prefetch_count=1)
            return True
        except Exception as e:
            print(f"检查连接状态时发生错误: {str(e)}")
            return False


class MQSender:
    """MQ消息发送器，用于测试发送消息到队列"""

    def __init__(self):
        self.connection = None
        self.channel = None

    def connect(self):
        """连接到RabbitMQ服务器"""
        try:
            print("正在连接RabbitMQ服务器（发送器）...")
            print(f"连接信息: {RABBITMQ_CONFIG}")

            credentials = pika.PlainCredentials(
                RABBITMQ_CONFIG['username'],
                RABBITMQ_CONFIG['password']
            )
            parameters = pika.ConnectionParameters(
                host=RABBITMQ_CONFIG['host'],
                port=RABBITMQ_CONFIG['port'],
                virtual_host=RABBITMQ_CONFIG['virtual_host'],
                credentials=credentials,
                heartbeat=60
            )
            self.connection = pika.BlockingConnection(parameters)
            self.channel = self.connection.channel()

            # 声明交换机和队列
            self.channel.exchange_declare(exchange=RABBITMQ_CONFIG['exchange'], exchange_type='direct', durable=True)
            self.channel.queue_declare(queue=RABBITMQ_CONFIG['queue_name'], durable=True)
            self.channel.queue_bind(
                exchange=RABBITMQ_CONFIG['exchange'],
                queue=RABBITMQ_CONFIG['queue_name'],
                routing_key=RABBITMQ_CONFIG['routing_key']
            )

            print("RabbitMQ发送器连接成功")
            return True
        except Exception as e:
            print(f"发送器连接过程中发生错误: {str(e)}")
            return False

    def send_test_message(self, **kwargs):
        """发送测试消息到队列

        Args:
            **kwargs: 消息参数，支持以下字段：
                - region: 区服名称 (默认: "测试区服")
                - guild: 帮会名称 (默认: "测试帮会")
                - appearance: 外观名称 (默认: "测试外观")
                - character_name: 角色名称 (默认: "测试角色")
                - order_id: 订单ID (默认: "TEST_ORDER_" + 时间戳)
                - is_split: 是否拆分 (默认: False)
                - gift_name: 礼盒名称 (默认: "")
                - gift_color: 礼盒颜色 (默认: "")
                - other_items: 其他物品 (默认: "")
                - sendCount: 发送数量 (默认: 1)
                - buyer_wangwang: 买家旺旺 (默认: "测试买家")
                - restart: 是否重启 (默认: False)
        """
        if not self.channel:
            print("未连接到RabbitMQ，无法发送消息")
            return False

        try:
            # 生成时间戳
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")

            # 构建默认消息
            default_message = {
                "region": "测试区服",
                "guild": "测试帮会",
                "appearance": "测试外观",
                "character_name": "测试角色",
                "order_id": f"TEST_ORDER_{timestamp}",
                "is_split": False,
                "gift_name": "",
                "gift_color": "",
                "other_items": "",
                "sendCount": 1,
                "buyer_wangwang": "测试买家",
                "restart": False
            }

            # 使用传入的参数覆盖默认值
            message = {**default_message, **kwargs}

            # 转换为JSON字符串
            message_body = json.dumps(message, ensure_ascii=False)

            # 发送消息
            self.channel.basic_publish(
                exchange=RABBITMQ_CONFIG['exchange'],
                routing_key=RABBITMQ_CONFIG['routing_key'],
                body=message_body.encode('utf-8'),
                properties=pika.BasicProperties(
                    delivery_mode=2,  # 消息持久化
                    timestamp=int(datetime.datetime.now().timestamp())
                )
            )

            print(f"测试消息发送成功: {message}")
            return True

        except Exception as e:
            print(f"发送测试消息失败: {str(e)}")
            print(traceback.format_exc())
            return False

    def send_custom_message(self, message_dict):
        """发送自定义消息到队列

        Args:
            message_dict: 完整的消息字典
        """
        if not self.channel:
            print("未连接到RabbitMQ，无法发送消息")
            return False

        try:
            # 转换为JSON字符串
            message_body = json.dumps(message_dict, ensure_ascii=False)

            # 发送消息
            self.channel.basic_publish(
                exchange=RABBITMQ_CONFIG['exchange'],
                routing_key=RABBITMQ_CONFIG['routing_key'],
                body=message_body.encode('utf-8'),
                properties=pika.BasicProperties(
                    delivery_mode=2,  # 消息持久化
                    timestamp=int(datetime.datetime.now().timestamp())
                )
            )

            print(f"自定义消息发送成功: {message_dict}")
            return True

        except Exception as e:
            print(f"发送自定义消息失败: {str(e)}")
            print(traceback.format_exc())
            return False

    def close(self):
        """关闭连接"""
        if self.connection:
            self.connection.close()
            print("MQ发送器连接已关闭")


class DeliveryNotificationSender:
    """出库完成通知发送器"""

    def __init__(self):
        self.connection = None
        self.channel = None
        # 出库通知的MQ配置
        self.config = {
            'exchange': 'TestDirectExchange',
            'queue': 'TestDirectQueue',
            'routing_key': 'TestDirectRouting'
        }

    def connect(self):
        """连接到RabbitMQ"""
        try:
            print("正在连接RabbitMQ服务器（出库通知）...")

            credentials = pika.PlainCredentials(
                RABBITMQ_CONFIG['username'],
                RABBITMQ_CONFIG['password']
            )
            parameters = pika.ConnectionParameters(
                host=RABBITMQ_CONFIG['host'],
                port=RABBITMQ_CONFIG['port'],
                virtual_host=RABBITMQ_CONFIG['virtual_host'],
                credentials=credentials,
                heartbeat=60
            )
            self.connection = pika.BlockingConnection(parameters)
            self.channel = self.connection.channel()

            # 声明交换机和队列
            self.channel.exchange_declare(
                exchange=self.config['exchange'],
                exchange_type='direct',
                durable=True
            )
            self.channel.queue_declare(
                queue=self.config['queue'],
                durable=True
            )
            self.channel.queue_bind(
                exchange=self.config['exchange'],
                queue=self.config['queue'],
                routing_key=self.config['routing_key']
            )

            print("✅ 出库通知MQ连接成功")
            return True

        except Exception as e:
            print(f"❌ 出库通知MQ连接失败: {str(e)}")
            return False

    def send_delivery_notification(self, order_id, is_split, split_name=None, warehouse="帮1"):
        """
        发送出库完成通知

        Args:
            order_id (int): 订单编号
            is_split (bool): 是否拆分 (True=1-是, False=2-否)
            split_name (str): 礼盒名称（拆分时使用）
            warehouse (str): 仓库名称，默认"帮1"
        """
        try:
            # 构建消息内容（按照Java端GameOrder格式）
            # 只包含必要字段，避免null值导致的序列化问题
            message = {
                "id": order_id,
                "isSplit": 1 if is_split else 2,  # 1-是 2-否
                "field2": warehouse,  # 仓库
                "field3": "99",  # 根据您的数据库更新，设置为99
                "operationType": 2  # 操作类型
            }

            # 只有在拆分时才添加splitName字段
            if is_split and split_name:
                message["splitName"] = split_name

            # 发送消息 - 设置正确的消息头以便Java端反序列化
            message_body = json.dumps(message, ensure_ascii=False)

            self.channel.basic_publish(
                exchange=self.config['exchange'],
                routing_key=self.config['routing_key'],
                body=message_body.encode('utf-8'),
                properties=pika.BasicProperties(
                    delivery_mode=2,  # 消息持久化
                    content_type='application/json',
                    content_encoding='UTF-8',
                    headers={
                        '__TypeId__': 'com.ruoyi.system.domain.GameOrder'  # 指定Java类型
                    }
                )
            )

            print(f"✅ 出库通知发送成功: 订单{order_id}, 拆分={is_split}, 礼盒={split_name}")
            return True

        except Exception as e:
            print(f"❌ 出库通知发送失败: {str(e)}")
            print(traceback.format_exc())
            return False

    def close(self):
        """关闭连接"""
        try:
            if self.channel and not self.channel.is_closed:
                self.channel.close()
            if self.connection and not self.connection.is_closed:
                self.connection.close()
            print("出库通知发送器连接已关闭")
        except Exception as e:
            print(f"关闭出库通知发送器连接时发生错误: {str(e)}")