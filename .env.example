# 腾讯云OCR配置
# 请将此文件复制为.env文件，并填入您的腾讯云密钥
TENCENT_SECRET_ID=your_secret_id_here
TENCENT_SECRET_KEY=your_secret_key_here

# MQ环境配置
# test=测试环境（默认），prod=生产环境
MQ_ENV=test

# MySQL配置（如果需要覆盖默认配置）
MYSQL_PORT=3306

# Redis配置（如果需要覆盖默认配置）
REDIS_HOST=*************
REDIS_PORT=6379
REDIS_PASSWORD=wj5201314
REDIS_DB=0

# Umi-OCR配置
UMI_OCR_PATH=./Umi-OCR.exe
UMI_OCR_API_URL=http://127.0.0.1:1224
UMI_OCR_USE_API=true
UMI_OCR_LANGUAGE=chinese
UMI_OCR_ENGINE=paddle
UMI_OCR_CONFIDENCE=0.7

# 游戏配置
# 游戏可执行文件路径 - 请根据实际安装路径修改
GAME_EXECUTABLE_PATH=D:\SeasunGame\Game\JX3_WJ\bin\vk_mb\bin64_m\JX3ClientX3DX64.exe
