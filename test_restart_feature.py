#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试重启功能的脚本
"""

from mq_listener import MQSender
import time

def test_restart_feature():
    """测试重启功能"""
    print("🧪 开始测试重启功能...")
    
    # 创建MQ发送器
    sender = MQSender()
    
    # 连接到RabbitMQ
    if not sender.connect():
        print("❌ 连接RabbitMQ失败")
        return False
    
    print("✅ 连接RabbitMQ成功")
    
    # 发送测试消息 - 不重启
    print("\n📤 发送测试消息1 - 不重启")
    success1 = sender.send_test_message(
        region="梦江南",
        guild="帮1", 
        appearance="测试外观1",
        character_name="测试角色1",
        order_id="TEST_001",
        sendCount=1,
        restart=False
    )
    
    if success1:
        print("✅ 测试消息1发送成功")
    else:
        print("❌ 测试消息1发送失败")
    
    time.sleep(2)
    
    # 发送测试消息 - 强制重启
    print("\n📤 发送测试消息2 - 强制重启")
    success2 = sender.send_test_message(
        region="梦江南",
        guild="帮1",
        appearance="测试外观2", 
        character_name="测试角色2",
        order_id="TEST_002",
        sendCount=1,
        restart=True  # 强制重启
    )
    
    if success2:
        print("✅ 测试消息2发送成功")
    else:
        print("❌ 测试消息2发送失败")
    
    # 关闭连接
    sender.close()
    
    print("\n🎯 测试完成！")
    print("请检查GUI界面是否正确显示了'是否重启'字段")
    print("并观察dm_main.py的日志输出中是否包含重启相关信息")
    
    return success1 and success2

if __name__ == "__main__":
    test_restart_feature()
