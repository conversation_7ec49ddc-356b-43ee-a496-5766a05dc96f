#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速GUI测试
验证按钮是否可见
"""

import tkinter as tk
from tkinter import ttk

def main():
    """快速测试GUI布局"""
    print("快速GUI测试 - 验证按钮可见性")
    print("=" * 40)
    
    root = tk.Tk()
    root.title("按钮可见性测试")
    root.geometry("400x380")
    root.minsize(width=400, height=320)
    
    # 创建主框架
    main_frame = ttk.Frame(root)
    main_frame.pack(fill=tk.BOTH, expand=True, padx=5, pady=5)
    
    # 左侧控制区域
    control_frame = ttk.Frame(main_frame)
    control_frame.pack(side=tk.LEFT, fill=tk.Y, padx=5)
    
    # 输入区域
    input_frame = ttk.LabelFrame(control_frame, text="发货信息", padding=5)
    input_frame.pack(fill=tk.X, pady=2)
    
    # 创建所有输入字段（按实际顺序）
    fields = [
        "区服",
        "帮会", 
        "外观",
        "角色名",
        "订单编号",
        "买家旺旺",
        "客服名称",  # 新增
        "淘宝订单",  # 新增
        "数量",
        "是否拆分"
    ]
    
    # 创建输入字段
    for i, field in enumerate(fields):
        ttk.Label(input_frame, text=f"{field}:").grid(row=i, column=0, sticky=tk.W, pady=1)
        if field == "是否拆分":
            ttk.Checkbutton(input_frame).grid(row=i, column=1, sticky=tk.W, pady=1)
        else:
            ttk.Entry(input_frame, width=15).grid(row=i, column=1, sticky=tk.W, pady=1)
    
    # 按钮区域 - 这是关键部分
    button_frame = ttk.Frame(control_frame)
    button_frame.pack(fill=tk.X, pady=5)
    
    # 创建按钮
    config_btn = ttk.Button(button_frame, text="账号配置", width=8)
    config_btn.pack(side=tk.LEFT, padx=2)
    
    start_btn = ttk.Button(button_frame, text="开始发货", width=8)
    start_btn.pack(side=tk.LEFT, padx=2)
    
    stop_btn = ttk.Button(button_frame, text="停止", width=8)
    stop_btn.pack(side=tk.LEFT, padx=2)
    
    # 右侧日志区域
    log_frame = ttk.Frame(main_frame)
    log_frame.pack(side=tk.RIGHT, fill=tk.BOTH, expand=True, padx=5)
    
    log_label = ttk.LabelFrame(log_frame, text="运行日志", padding=5)
    log_label.pack(fill=tk.BOTH, expand=True)
    
    log_text = tk.Text(log_label, width=30, height=10)
    log_text.pack(fill=tk.BOTH, expand=True)
    log_text.insert(tk.END, "测试日志区域\n")
    log_text.insert(tk.END, "请检查左侧是否能看到三个按钮:\n")
    log_text.insert(tk.END, "1. 账号配置\n")
    log_text.insert(tk.END, "2. 开始发货\n") 
    log_text.insert(tk.END, "3. 停止\n\n")
    log_text.insert(tk.END, "如果看不到按钮，说明窗口高度还需要调整。\n")
    
    # 状态栏
    status_frame = ttk.Frame(root)
    status_frame.pack(side=tk.BOTTOM, fill=tk.X)
    ttk.Label(status_frame, text="状态: 测试中").pack(pady=2)
    
    print("✅ 测试窗口已创建")
    print("请检查是否能看到三个按钮:")
    print("- 账号配置")
    print("- 开始发货") 
    print("- 停止")
    print()
    print("如果看不到按钮，请关闭窗口并报告问题")
    
    # 添加按钮点击测试
    def test_click(btn_name):
        log_text.insert(tk.END, f"✅ {btn_name} 按钮可点击!\n")
        log_text.see(tk.END)
    
    config_btn.configure(command=lambda: test_click("账号配置"))
    start_btn.configure(command=lambda: test_click("开始发货"))
    stop_btn.configure(command=lambda: test_click("停止"))
    
    root.mainloop()
    
    print("测试完成")

if __name__ == "__main__":
    main()
