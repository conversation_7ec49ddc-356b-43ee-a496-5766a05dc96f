#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速MQ消息发送工具
支持命令行参数快速发送测试消息
"""

import argparse
import sys
from mq_listener import MQSender

def create_parser():
    """创建命令行参数解析器"""
    parser = argparse.ArgumentParser(
        description="快速发送MQ测试消息",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
使用示例:
  # 发送基础测试消息
  python quick_send_mq.py
  
  # 发送自定义消息
  python quick_send_mq.py --region "华北一区" --guild "测试帮会" --appearance "炫彩外观"
  
  # 发送拆分消息
  python quick_send_mq.py --split --gift-name "神秘礼盒" --gift-color "金色"
  
  # 发送多数量消息
  python quick_send_mq.py --count 10
  
  # 批量发送
  python quick_send_mq.py --batch 5
        """
    )
    
    # 基础参数
    parser.add_argument('--region', '-r', default='测试区服', help='区服名称 (默认: 测试区服)')
    parser.add_argument('--guild', '-g', default='测试帮会', help='帮会名称 (默认: 测试帮会)')
    parser.add_argument('--appearance', '-a', default='测试外观', help='外观名称 (默认: 测试外观)')
    parser.add_argument('--character', '-c', default='测试角色', help='角色名称 (默认: 测试角色)')
    parser.add_argument('--buyer', '-b', default='测试买家', help='买家旺旺 (默认: 测试买家)')
    
    # 拆分相关参数
    parser.add_argument('--split', '-s', action='store_true', help='是否拆分')
    parser.add_argument('--gift-name', default='', help='礼盒名称 (拆分时使用)')
    parser.add_argument('--gift-color', default='', help='礼盒颜色 (拆分时使用)')
    
    # 数量和其他参数
    parser.add_argument('--count', '-n', type=int, default=1, help='发送数量 (默认: 1)')
    parser.add_argument('--other-items', default='', help='其他物品')
    parser.add_argument('--order-id', default='', help='订单ID (留空自动生成)')
    
    # 批量发送
    parser.add_argument('--batch', type=int, help='批量发送消息数量')
    
    # 其他选项
    parser.add_argument('--verbose', '-v', action='store_true', help='显示详细信息')
    
    return parser

def send_single_message(args):
    """发送单个消息"""
    sender = MQSender()
    
    if not sender.connect():
        print("❌ 连接MQ失败")
        return False
    
    try:
        # 构建消息参数
        message_params = {
            'region': args.region,
            'guild': args.guild,
            'appearance': args.appearance,
            'character_name': args.character,
            'buyer_wangwang': args.buyer,
            'is_split': args.split,
            'gift_name': args.gift_name,
            'gift_color': args.gift_color,
            'sendCount': args.count,
            'other_items': args.other_items
        }
        
        # 如果指定了订单ID，则使用指定的
        if args.order_id:
            message_params['order_id'] = args.order_id
        
        if args.verbose:
            print("发送消息参数:")
            for key, value in message_params.items():
                print(f"  {key}: {value}")
            print()
        
        success = sender.send_test_message(**message_params)
        
        if success:
            print("✅ 消息发送成功")
            return True
        else:
            print("❌ 消息发送失败")
            return False
            
    finally:
        sender.close()

def send_batch_messages(args):
    """批量发送消息"""
    sender = MQSender()
    
    if not sender.connect():
        print("❌ 连接MQ失败")
        return False
    
    try:
        success_count = 0
        total_count = args.batch
        
        print(f"开始批量发送 {total_count} 条消息...")
        
        for i in range(total_count):
            # 为每条消息添加序号
            message_params = {
                'region': f"{args.region}_{i+1}",
                'guild': f"{args.guild}_{i+1}",
                'appearance': f"{args.appearance}_{i+1}",
                'character_name': f"{args.character}_{i+1}",
                'buyer_wangwang': f"{args.buyer}_{i+1}",
                'is_split': args.split,
                'gift_name': args.gift_name,
                'gift_color': args.gift_color,
                'sendCount': args.count,
                'other_items': args.other_items
            }
            
            if args.verbose:
                print(f"发送第 {i+1} 条消息: {message_params['region']}")
            
            success = sender.send_test_message(**message_params)
            
            if success:
                success_count += 1
                if not args.verbose:
                    print(f"✅ {i+1}/{total_count}")
            else:
                if not args.verbose:
                    print(f"❌ {i+1}/{total_count}")
        
        print(f"\n批量发送完成: {success_count}/{total_count} 条消息发送成功")
        return success_count == total_count
        
    finally:
        sender.close()

def main():
    """主函数"""
    parser = create_parser()
    args = parser.parse_args()
    
    try:
        if args.batch:
            # 批量发送模式
            success = send_batch_messages(args)
        else:
            # 单个消息发送模式
            success = send_single_message(args)
        
        # 设置退出码
        sys.exit(0 if success else 1)
        
    except KeyboardInterrupt:
        print("\n程序被用户中断")
        sys.exit(1)
    except Exception as e:
        print(f"发生错误: {str(e)}")
        if args.verbose:
            import traceback
            traceback.print_exc()
        sys.exit(1)

if __name__ == "__main__":
    main()
