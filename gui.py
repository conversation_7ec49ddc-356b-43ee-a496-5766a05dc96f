import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import json
import os
import threading
import queue
import time
from dm_main import GameScript
import pythoncom
from mq_listener import M<PERSON>istener, GameMessage  # 导入MQListener和GameMessage
import keyboard  # 添加到文件开头的导入部分
import pika


class DeliveryGUI:
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("游戏发货工具")

        # 添加temp_config初始化
        self.temp_config = {}

        # 添加在初始化部分
        self.setup_emergency_stop()

        self.root.geometry("400x380")  # 调整窗口尺寸以适应新字段
        self.root.minsize(width=400, height=320)  # 增加高度以适应新增的两个字段

        self.root.attributes('-topmost', True)  # 窗口置顶
        
        # 获取屏幕宽度和窗口宽度
        screen_width = self.root.winfo_screenwidth()
        window_width = 400
        
        # 设置窗口位置到右上角
        x = screen_width - window_width
        self.root.geometry(f"{window_width}x380+{x}+0")  # 增加窗口高度以容纳新增字段
        
        # 设置配置文件路径
        self.config_path = os.path.join(os.path.dirname(os.path.abspath(__file__)), 'account_config.json')
        
        # 加载账号配置
        self.account_config = self.load_account_config()
        
        # 创建GameScript实例并传入stop_flag
        self.stop_flag = threading.Event()
        self.script = GameScript(stop_event=self.stop_flag)  # 初始化GameScript实例

        # 创建主框架
        main_frame = ttk.Frame(self.root, padding="5")  # 减小padding
        main_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S))
        
        # 左侧控制面板
        control_frame = ttk.Frame(main_frame)
        control_frame.grid(row=0, column=0, sticky=(tk.W, tk.E, tk.N, tk.S), padx=2)  # 减小间距
        
        # 创建输入区域
        input_frame = ttk.Frame(control_frame)
        input_frame.pack(fill=tk.X, pady=2)
        
        # 区服选择
        ttk.Label(input_frame, text="区服:").grid(row=0, column=0, sticky=tk.W, pady=2)
        self.region_var = tk.StringVar()
        self.region_combo = ttk.Combobox(input_frame, textvariable=self.region_var, width=15)
        self.region_combo['values'] = ['梦江南','乾坤一掷','唯我独尊','天鹅坪','破阵子','剑胆琴心','幽月轮','斗转星移','绝代天骄']
        self.region_combo.grid(row=0, column=1, sticky=tk.W, pady=2)
        self.region_combo.set('梦江南')
        
        # 帮会选择
        ttk.Label(input_frame, text="帮会:").grid(row=1, column=0, sticky=tk.W, pady=2)
        self.guild_var = tk.StringVar()
        self.guild_combo = ttk.Combobox(input_frame, textvariable=self.guild_var, width=15)
        self.guild_combo['values'] = ['帮1', '帮2', '帮3', '帮4', '帮5', '帮6', '帮7', '帮8', '帮9','帮11']
        self.guild_combo.grid(row=1, column=1, sticky=tk.W, pady=2)
        self.guild_combo.set('帮1')
        
        # 外观输入
        ttk.Label(input_frame, text="外观:").grid(row=2, column=0, sticky=tk.W, pady=2)
        self.appearance_var = tk.StringVar()
        ttk.Entry(input_frame, textvariable=self.appearance_var, width=17).grid(row=2, column=1, sticky=tk.W, pady=2)

        # 数量输入
        ttk.Label(input_frame, text="数量:").grid(row=3, column=0, sticky=tk.W, pady=2)
        self.sendCount_var = tk.StringVar()
        ttk.Entry(input_frame, textvariable=self.sendCount_var, width=17).grid(row=3, column=1, sticky=tk.W, pady=2)
        
        # 角色名称输入
        ttk.Label(input_frame, text="角色:").grid(row=4, column=0, sticky=tk.W, pady=2)
        self.character_name_var = tk.StringVar()
        ttk.Entry(input_frame, textvariable=self.character_name_var, width=17).grid(row=4, column=1, sticky=tk.W, pady=2)

        # 订单ID输入
        ttk.Label(input_frame, text="订单ID:").grid(row=5, column=0, sticky=tk.W, pady=2)
        self.order_id_var = tk.StringVar()
        ttk.Entry(input_frame, textvariable=self.order_id_var, width=17).grid(row=5, column=1, sticky=tk.W, pady=2)

        # 买家旺旺输入（一直显示）
        ttk.Label(input_frame, text="买家旺旺:").grid(row=6, column=0, sticky=tk.W, pady=2)
        self.buyer_wangwang_var = tk.StringVar()
        ttk.Entry(input_frame, textvariable=self.buyer_wangwang_var, width=17).grid(row=6, column=1, sticky=tk.W, pady=2)

        # 客服名称输入
        ttk.Label(input_frame, text="客服名称:").grid(row=7, column=0, sticky=tk.W, pady=2)
        self.user_nick_var = tk.StringVar()
        ttk.Entry(input_frame, textvariable=self.user_nick_var, width=17).grid(row=7, column=1, sticky=tk.W, pady=2)

        # 淘宝订单输入
        ttk.Label(input_frame, text="淘宝订单:").grid(row=8, column=0, sticky=tk.W, pady=2)
        self.taobao_id_var = tk.StringVar()
        ttk.Entry(input_frame, textvariable=self.taobao_id_var, width=17).grid(row=8, column=1, sticky=tk.W, pady=2)

        # 是否拆分复选框和是否重启复选框并排显示
        split_restart_frame = ttk.Frame(input_frame)
        split_restart_frame.grid(row=9, column=0, columnspan=2, sticky=tk.W, pady=2)

        # 是否拆分复选框
        ttk.Label(split_restart_frame, text="是否拆分:").grid(row=0, column=0, sticky=tk.W, padx=(0, 5))
        self.split_var = tk.BooleanVar()
        self.split_var.set(False)  # 默认否
        split_checkbox = ttk.Checkbutton(split_restart_frame, variable=self.split_var, command=self.toggle_split_fields)
        split_checkbox.grid(row=0, column=1, sticky=tk.W, padx=(0, 20))

        # 是否重启复选框
        ttk.Label(split_restart_frame, text="是否重启:").grid(row=0, column=2, sticky=tk.W, padx=(0, 5))
        self.restart_var = tk.BooleanVar()
        self.restart_var.set(False)  # 默认否
        restart_checkbox = ttk.Checkbutton(split_restart_frame, variable=self.restart_var)
        restart_checkbox.grid(row=0, column=3, sticky=tk.W)

        # 礼盒名称输入（条件显示）
        self.gift_name_label = ttk.Label(input_frame, text="礼盒名称:")
        self.gift_name_label.grid(row=10, column=0, sticky=tk.W, pady=2)
        self.gift_name_var = tk.StringVar()
        self.gift_name_entry = ttk.Entry(input_frame, textvariable=self.gift_name_var, width=17)
        self.gift_name_entry.grid(row=10, column=1, sticky=tk.W, pady=2)

        # 礼盒颜色输入（条件显示）
        self.gift_color_label = ttk.Label(input_frame, text="礼盒颜色:")
        self.gift_color_label.grid(row=11, column=0, sticky=tk.W, pady=2)
        self.gift_color_var = tk.StringVar()
        self.gift_color_entry = ttk.Entry(input_frame, textvariable=self.gift_color_var, width=17)
        self.gift_color_entry.grid(row=11, column=1, sticky=tk.W, pady=2)

        # 其余散件输入（条件显示）
        self.other_items_label = ttk.Label(input_frame, text="其余散件:")
        self.other_items_label.grid(row=12, column=0, sticky=tk.W, pady=2)
        self.other_items_var = tk.StringVar()
        self.other_items_entry = ttk.Entry(input_frame, textvariable=self.other_items_var, width=17)
        self.other_items_entry.grid(row=12, column=1, sticky=tk.W, pady=2)

        # 初始隐藏礼盒相关字段
        self.hide_gift_fields()
        
        # 按钮区域
        button_frame = ttk.Frame(control_frame)
        button_frame.pack(fill=tk.X, pady=2)
        
        # 水平排列按钮
        self.config_button = ttk.Button(button_frame, text="账号配置", command=self.show_account_config, width=8)
        self.config_button.pack(side=tk.LEFT, padx=2)
        
        self.start_button = ttk.Button(button_frame, text="开始发货", command=self.start_delivery, width=8)
        self.start_button.pack(side=tk.LEFT, padx=2)
        
        self.stop_button = ttk.Button(button_frame, text="清空队列", command=self.stop_delivery, state=tk.DISABLED, width=8)
        self.stop_button.pack(side=tk.LEFT, padx=2)
        
        # 右侧日志区域
        log_frame = ttk.Frame(main_frame)
        log_frame.grid(row=0, column=1, sticky=(tk.W, tk.E, tk.N, tk.S), padx=2)
        
        # 日志显示（减小高度）
        self.log_text = scrolledtext.ScrolledText(log_frame, width=20, height=5)
        self.log_text.pack(fill=tk.BOTH, expand=True)
        
        # 状态栏
        self.status_var = tk.StringVar(value="就绪")
        ttk.Label(main_frame, textvariable=self.status_var).grid(row=1, column=0, columnspan=2, pady=2)
        
        # 初始化任务相关变量
        self.task_thread = None
        self.stop_flag = threading.Event()  # 使用Event来控制停止
        self.log_queue = queue.Queue()

        # 启动日志更新
        self.root.after(100, self.update_log)

        # 启动MQ监听
        self.start_mq_listener()

    def toggle_split_fields(self):
        """切换礼盒相关字段的显示/隐藏"""
        if self.split_var.get():
            # 显示礼盒相关字段
            self.show_gift_fields()
        else:
            # 隐藏礼盒相关字段
            self.hide_gift_fields()

    def show_gift_fields(self):
        """显示礼盒相关字段"""
        self.gift_name_label.grid()
        self.gift_name_entry.grid()
        self.gift_color_label.grid()
        self.gift_color_entry.grid()
        self.other_items_label.grid()
        self.other_items_entry.grid()
        # 调整窗口高度（礼盒字段显示）
        screen_width = self.root.winfo_screenwidth()
        window_width = 400
        x = screen_width - window_width
        self.root.geometry(f"{window_width}x380+{x}+0")

    def hide_gift_fields(self):
        """隐藏礼盒相关字段"""
        self.gift_name_label.grid_remove()
        self.gift_name_entry.grid_remove()
        self.gift_color_label.grid_remove()
        self.gift_color_entry.grid_remove()
        self.other_items_label.grid_remove()
        self.other_items_entry.grid_remove()
        # 恢复窗口高度（买家旺旺字段一直显示）
        screen_width = self.root.winfo_screenwidth()
        window_width = 400
        x = screen_width - window_width
        self.root.geometry(f"{window_width}x320+{x}+0")
    
    def start_mq_listener(self):
        """启动MQ监听"""
        print("启动MQ监听...")
        if self.script is None:
            print("GameScript实例未初始化，无法启动MQ监听")
            return

        def run_listener():
            listener = MQListener(self.script)  # 创建MQListener实例
            if not listener.connect():  # 确保连接成功
                print("MQListener连接失败，无法开始监听")
                return

            # 将MQListener实例保存到GameScript中，以便停止按钮可以访问
            self.script.mq_listener = listener

            listener.start_listening(self.handle_game_message)  # 监听消息

        threading.Thread(target=run_listener, daemon=True).start()

    def handle_game_message(self, message):
        """处理接收到的游戏消息"""
        self.region_var.set(message.region)
        self.guild_var.set(message.guild)
        self.appearance_var.set(message.appearance)
        self.sendCount_var.set(message.sendCount)
        self.character_name_var.set(message.character_name)
        self.order_id_var.set(message.order_id)

        # 设置拆分相关字段
        self.split_var.set(message.is_split)
        self.gift_name_var.set(message.gift_name)
        self.gift_color_var.set(message.gift_color)
        self.other_items_var.set(message.other_items)
        self.buyer_wangwang_var.set(message.buyer_wangwang)

        # 设置重启字段
        if hasattr(message, 'restart'):
            self.restart_var.set(message.restart)
        else:
            self.restart_var.set(False)  # 默认值

        # 根据is_split状态显示/隐藏礼盒字段
        if message.is_split:
            self.show_gift_fields()
        else:
            self.hide_gift_fields()

        # 直接执行游戏脚本任务
        self.execute_game_task_simple(message)

    def execute_game_task_simple(self, message):
        """执行游戏任务（从MQ消息触发）"""
        print(f"GUI收到消息，开始处理: {message.order_id}")

        # 禁用开始按钮，启用停止按钮
        self.start_button.configure(state=tk.DISABLED)
        self.stop_button.configure(state=tk.NORMAL)
        self.config_button.configure(state=tk.DISABLED)

        # 重置停止标志
        self.stop_flag.clear()
        self.status_var.set("正在执行发货任务...")

        # 重定向GameScript的输出
        class LogRedirector:
            def __init__(self, gui):
                self.gui = gui

            def write(self, message):
                if message.strip():  # 忽略空行
                    self.gui.log(message.strip())

            def flush(self):
                pass

        # 真正执行任务
        def run_real_task():
            try:
                pythoncom.CoInitialize()

                import sys
                old_stdout = sys.stdout
                sys.stdout = LogRedirector(self)

                self.status_var.set("正在执行发货任务...")

                # 调用GameScript的handle_game_message方法执行真正的发货任务
                self.script.handle_game_message(message)

                if self.stop_flag.is_set():
                    self.status_var.set("任务已停止")
                else:
                    self.status_var.set("发货任务完成")

                # 确认消息
                try:
                    if hasattr(self.script, 'mq_channel') and hasattr(self.script, 'mq_delivery_tag'):
                        # 检查通道是否仍然打开
                        if not self.script.mq_channel.is_open:
                            self.log("MQ通道已关闭，无法确认消息")
                            return

                        try:
                            self.script.mq_channel.basic_ack(delivery_tag=self.script.mq_delivery_tag)
                            self.log("消息已确认")
                        except pika.exceptions.ChannelClosedByBroker as e:
                            self.log(f"MQ通道已关闭，无法确认消息: {str(e)}")
                        except pika.exceptions.ChannelWrongStateError as e:
                            self.log(f"MQ通道状态错误，无法确认消息: {str(e)}")
                        except Exception as e:
                            self.log(f"消息确认失败: {str(e)}")
                    else:
                        self.log("无法确认消息：缺少mq_channel或mq_delivery_tag")
                except Exception as e:
                    self.log(f"消息确认过程发生错误: {str(e)}")

                sys.stdout = old_stdout

            except Exception as e:
                self.log(f"发货失败: {str(e)}")
                self.status_var.set(f"发货失败: {str(e)}")
                # 在失败时也尝试确认消息
                if hasattr(self.script, 'mq_channel') and hasattr(self.script, 'mq_delivery_tag'):
                    try:
                        if self.script.mq_channel.is_open:
                            self.script.mq_channel.basic_ack(delivery_tag=self.script.mq_delivery_tag)
                            self.log("消息已确认（失败后）")
                    except Exception as e:
                        self.log(f"消息确认失败: {str(e)}")
            finally:
                pythoncom.CoUninitialize()
                self.root.after(0, self.reset_buttons)

        # 启动真正的任务
        self.task_thread = threading.Thread(target=run_real_task)
        self.task_thread.daemon = True
        self.task_thread.start()

    def log(self, message):
        """添加日志到队列"""
        self.log_queue.put(f"[{time.strftime('%H:%M:%S')}] {message}\n")
    
    def update_log(self):
        """更新日志显示"""
        while not self.log_queue.empty():
            message = self.log_queue.get()
            self.log_text.insert(tk.END, message)
            self.log_text.see(tk.END)
        self.root.after(100, self.update_log)
    
    def load_account_config(self):
        """加载账号配置"""
        try:
            if os.path.exists(self.config_path):
                with open(self.config_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
        except Exception as e:
            print(f"加载配置文件失败: {str(e)}")
        return {f"帮{i}": {"account": "", "password": ""} for i in range(1, 10)}
    
    def save_account_config(self):
        """保存账号配置"""
        try:
            with open(self.config_path, 'w', encoding='utf-8') as f:
                json.dump(self.account_config, f, ensure_ascii=False, indent=2)
            return True
        except Exception as e:
            messagebox.showerror("错误", f"保存配置失败: {str(e)}")
            return False
    
    def show_account_config(self):
        """显示账号配置窗口"""
        config_window = tk.Toplevel(self.root)
        config_window.title("账号配置")
        config_window.geometry("300x400")
        config_window.attributes('-topmost', True)
        
        # 设置配置窗口位置到右上角
        screen_width = config_window.winfo_screenwidth()
        window_width = 250
        x = screen_width - window_width
        config_window.geometry(f"{window_width}x900+{x}+0")
        
        # 创建带滚动条的画布
        canvas = tk.Canvas(config_window)
        scrollbar = ttk.Scrollbar(config_window, orient="vertical", command=canvas.yview)
        scrollable_frame = ttk.Frame(canvas)
        
        # 配置画布滚动
        scrollable_frame.bind(
            "<Configure>",
            lambda e: canvas.configure(scrollregion=canvas.bbox("all"))
        )
        scrollable_frame_id = canvas.create_window((0, 0), window=scrollable_frame, anchor="nw")
        # 当画布大小改变时，调整scrollable_frame的宽度
        def on_canvas_configure(event):
            canvas.itemconfig(scrollable_frame_id, width=event.width)
    
        canvas.bind("<Configure>", on_canvas_configure)
        canvas.configure(yscrollcommand=scrollbar.set)
        
        # 为每个帮会创建账号密码输入框
        for guild in [f"帮{i}" for i in range(1, 12)]:
            frame = ttk.LabelFrame(scrollable_frame, text=guild, padding="2")
            frame.pack(fill=tk.X, padx=2, pady=1)
            
            # 账号输入框
            f1 = ttk.Frame(frame)
            f1.pack(fill=tk.X, pady=1)
            ttk.Label(f1, text="账号:", width=5).pack(side=tk.LEFT)
            account_var = tk.StringVar(value=self.account_config[guild]["account"])
            ttk.Entry(f1, textvariable=account_var, width=25).pack(side=tk.LEFT, padx=2)
            
            # 密码输入框
            f2 = ttk.Frame(frame)
            f2.pack(fill=tk.X, pady=1)
            ttk.Label(f2, text="密码:", width=5).pack(side=tk.LEFT)
            password_var = tk.StringVar(value=self.account_config[guild]["password"])
            ttk.Entry(f2, textvariable=password_var, show="*", width=25).pack(side=tk.LEFT, padx=2)
            
            self.temp_config[guild] = {
                "account": account_var,
                "password": password_var
            }
        
        # 保存按钮
        def save_config():
            try:
                for guild in self.temp_config:
                    self.account_config[guild] = {
                        "account": self.temp_config[guild]["account"].get(),
                        "password": self.temp_config[guild]["password"].get()
                    }
                if self.save_account_config():
                    messagebox.showinfo("提示", "配置已保存")
                    config_window.destroy()
            except Exception as e:
                messagebox.showerror("错误", f"保存配置失败: {str(e)}")
        
        save_button = ttk.Button(config_window, text="保存", command=save_config)
        save_button.pack(side=tk.BOTTOM, pady=2)
        
        # 放置画布和滚动条
        canvas.pack(side=tk.LEFT, fill=tk.BOTH, expand=True, padx=2, pady=2)
        scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
    
    def start_delivery(self):
        """开始发货（异步）"""
        # 获取当前选择的值
        region = self.region_var.get()
        guild = self.guild_var.get()
        appearance = self.appearance_var.get()
        sendCount = self.sendCount_var.get()
        print(f"数量: {sendCount}")
        character_name = self.character_name_var.get()
        order_id = self.order_id_var.get()

        # 获取新增字段
        is_split = self.split_var.get()
        gift_name = self.gift_name_var.get()
        gift_color = self.gift_color_var.get()
        buyer_wangwang = self.buyer_wangwang_var.get()
        user_nick = self.user_nick_var.get()
        taobao_id = self.taobao_id_var.get()
        restart = self.restart_var.get()

        # 检查基本输入
        if not all([region, guild, appearance, character_name, sendCount]):
            messagebox.showerror("错误", "请填写所有必要信息")
            return

        # 如果选择了拆分，礼盒名称必填
        if is_split and not gift_name:
            messagebox.showerror("错误", "选择拆分时，礼盒名称为必填项")
            return
        
        # 获取对应帮会的账号密码
        account_info = self.account_config.get(guild)
        if not account_info or not account_info["account"] or not account_info["password"]:
            messagebox.showerror("错误", f"请先配置{guild}的账号密码")
            return
        
        # 禁用开始按钮，启用停止按钮
        self.start_button.configure(state=tk.DISABLED)
        self.stop_button.configure(state=tk.NORMAL)
        self.config_button.configure(state=tk.DISABLED)
        
        # 重置停止标志
        self.stop_flag.clear()
        
        # 创建消息对象
        message = GameMessage(
            account_info["account"],
            account_info["password"],
            region,
            guild,
            appearance,
            character_name,
            order_id,
            is_split,
            gift_name,
            gift_color,
            self.other_items_var.get(),
            sendCount,
            buyer_wangwang,
            user_nick,
            taobao_id,
            restart
        )
        
        # 重定向GameScript的输出
        class LogRedirector:
            def __init__(self, gui):
                self.gui = gui
            
            def write(self, message):
                if message.strip():  # 忽略空行
                    self.gui.log(message.strip())
            
            def flush(self):
                pass
        
        # 创建并启动任务线程
        def run_task():
            try:
                pythoncom.CoInitialize()
                
                import sys
                old_stdout = sys.stdout
                sys.stdout = LogRedirector(self)
                
                self.status_var.set("正在执行发货任务...")
                self.script.handle_game_message(message)
                
                if self.stop_flag.is_set():
                    self.status_var.set("任务已停止")
                else:
                    self.status_var.set("发货任务完成")
                
                # 确认消息
                try:
                    if hasattr(self.script, 'mq_channel') and hasattr(self.script, 'mq_delivery_tag'):
                        # 检查通道是否仍然打开
                        if not self.script.mq_channel.is_open:
                            self.log("MQ通道已关闭，无法确认消息")
                            return
                            
                        try:
                            self.script.mq_channel.basic_ack(delivery_tag=self.script.mq_delivery_tag)
                            self.log("消息已确认")
                        except pika.exceptions.ChannelClosedByBroker as e:
                            self.log(f"MQ通道已关闭，无法确认消息: {str(e)}")
                        except pika.exceptions.ChannelWrongStateError as e:
                            self.log(f"MQ通道状态错误，无法确认消息: {str(e)}")
                        except Exception as e:
                            self.log(f"消息确认失败: {str(e)}")
                    else:
                        self.log("无法确认消息：缺少mq_channel或mq_delivery_tag")
                except Exception as e:
                    self.log(f"消息确认过程发生错误: {str(e)}")
                    
                sys.stdout = old_stdout
                
            except Exception as e:
                self.log(f"发货失败: {str(e)}")
                self.status_var.set(f"发货失败: {str(e)}")
                # 在失败时也尝试确认消息
                if hasattr(self.script, 'mq_channel') and hasattr(self.script, 'mq_delivery_tag'):
                    try:
                        if self.script.mq_channel.is_open:
                            self.script.mq_channel.basic_ack(delivery_tag=self.script.mq_delivery_tag)
                            self.log("消息已确认（失败后）")
                    except Exception as e:
                        self.log(f"消息确认失败: {str(e)}")
            finally:
                pythoncom.CoUninitialize()
                self.root.after(0, self.reset_buttons)
        
        self.task_thread = threading.Thread(target=run_task)
        self.task_thread.daemon = True
        self.task_thread.start()
    
    def stop_delivery(self):
        """停止发货任务并消费队列中的所有MQ消息"""
        try:
            # 消费队列中的所有消息
            if hasattr(self.script, 'mq_listener') and self.script.mq_listener:
                self.log("🔄 开始消费队列中的所有消息...")
                self.status_var.set("正在消费所有消息...")

                success = self.script.mq_listener.consume_all_messages()
                if success:
                    self.log("✅ 已消费队列中的所有消息")
                    self.status_var.set("已消费所有消息")
                else:
                    self.log("❌ 消费消息失败")
                    self.status_var.set("消费消息失败")
            else:
                self.log("⚠️ MQ监听器不可用")
                self.status_var.set("MQ监听器不可用")

            # 设置停止标志
            if self.script:
                self.stop_flag.set()
                self.log("正在停止任务...")

            # 重置按钮状态
            self.reset_buttons()

        except Exception as e:
            self.log(f"❌ 停止任务时发生错误: {str(e)}")
            self.status_var.set("停止任务失败")
    
    def reset_buttons(self):
        """重置按钮状态"""
        self.start_button.configure(state=tk.NORMAL)
        self.stop_button.configure(state=tk.DISABLED)
        self.config_button.configure(state=tk.NORMAL)

    def setup_emergency_stop(self):
        """设置紧急停止热键"""
        keyboard.on_press_key("F12", self.emergency_stop)
        # 添加提示标签
        emergency_label = ttk.Label(
            self.root, 
            text="按 F12 紧急停止",
            foreground="red"
        )
        emergency_label.grid(row=2, column=0, columnspan=2, pady=2)

    def emergency_stop(self, e):
        """紧急停止处理函数"""
        try:
            print("触发紧急停止！")
            if self.script:
                self.stop_flag.set()  # 设置停止标志
                self.script.cleanup()  # 清理资源
            self.root.after(0, self.root.destroy)  # 安全地关闭窗口
            os._exit(0)  # 强制终止程序
        except Exception as e:
            print(f"紧急停止时发生错误: {str(e)}")
            os._exit(1)
    
    def run(self):
        """运行GUI"""
        try:
            self.root.mainloop()
        except KeyboardInterrupt:
            self.emergency_stop(None)
        except Exception as e:
            print(f"程序异常: {str(e)}")
            self.emergency_stop(None)

if __name__ == "__main__":
    gui = DeliveryGUI()
    gui.run() 