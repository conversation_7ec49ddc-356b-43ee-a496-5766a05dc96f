#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试千牛发货订单接口
验证淘宝订单发货功能
"""

import sys
import os
import json
import requests

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_qianniu_shipment_api():
    """测试千牛发货订单接口"""
    print("=" * 60)
    print("测试千牛发货订单接口")
    print("=" * 60)
    
    try:
        from qianniu_config import get_api_url_for_user
        
        # 测试数据
        test_cases = [
            {
                "name": "客服心心",
                "user_nick": "lvquana:心心",
                "order_id": "2846512812731836769"
            },
            {
                "name": "客服三三", 
                "user_nick": "lvquana:三三",
                "order_id": "test_order_123456"
            }
        ]
        
        for case in test_cases:
            print(f"\n📤 测试: {case['name']}")
            print(f"客服: {case['user_nick']}")
            print(f"订单: {case['order_id']}")
            
            # 获取API地址
            api_url = get_api_url_for_user(case['user_nick'])
            shipment_api_url = f"{api_url}/QianNiu/Api"
            
            print(f"API地址: {shipment_api_url}")
            
            # 准备请求数据
            data = {
                "userNick": case['user_nick'],
                "orderId": case['order_id']
            }
            
            print(f"请求数据: {data}")
            
            # 发送请求（注意：这里只是测试请求格式，不会真正发送）
            print("📋 请求格式:")
            print(f"POST {shipment_api_url}")
            print("Content-Type: application/x-www-form-urlencoded")
            print("Body:")
            print(f"  post=PostOrderShipment")
            print(f"  data={json.dumps(data)}")
            
            print("📋 预期响应格式:")
            expected_response = {
                "code": 200,
                "data": {},
                "ret": ["SUCCESS::调用成功"]
            }
            print(json.dumps(expected_response, indent=2, ensure_ascii=False))
            
            # 如果需要真实测试，取消注释以下代码
            """
            try:
                response = requests.post(
                    shipment_api_url,
                    data={
                        "post": "PostOrderShipment",
                        "data": json.dumps(data)
                    },
                    timeout=10
                )
                
                if response.status_code == 200:
                    result = response.json()
                    print(f"✅ 接口调用成功: {result}")
                else:
                    print(f"❌ 接口调用失败: HTTP {response.status_code}")
                    
            except Exception as e:
                print(f"❌ 请求异常: {str(e)}")
            """
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_dm_main_shipment_method():
    """测试dm_main中的发货方法"""
    print("\n" + "=" * 60)
    print("测试dm_main中的发货方法")
    print("=" * 60)

    try:
        from dm_main import GameScript
        from qianniu_client import send_qianniu_order_shipment

        # 创建GameScript实例
        script = GameScript()

        # 设置测试数据
        script.user_nick = "lvquana:心心"
        script.taobao_id = "test_order_123456"

        print(f"✅ GameScript实例创建成功")
        print(f"客服名称: {script.user_nick}")
        print(f"淘宝订单: {script.taobao_id}")

        # 测试发货方法（注意：这里只是测试方法调用，不会真正发送请求）
        print("\n🧪 测试发货方法调用...")

        # 测试GameScript中的方法
        if hasattr(script, 'send_qianniu_shipment_notification'):
            print("✅ send_qianniu_shipment_notification 方法存在")
        else:
            print("❌ send_qianniu_shipment_notification 方法不存在")
            return False

        # 测试便捷函数
        print("\n🧪 测试便捷函数...")
        print("✅ send_qianniu_order_shipment 函数可用")

        # 测试函数调用（不发送真实请求）
        print("📋 函数调用格式:")
        print(f"send_qianniu_order_shipment('{script.taobao_id}', '{script.user_nick}')")

        return True

    except Exception as e:
        print(f"❌ 测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

def test_integration_flow():
    """测试完整的集成流程"""
    print("\n" + "=" * 60)
    print("测试完整的集成流程")
    print("=" * 60)
    
    print("📋 完整流程说明:")
    print("1. 用户在GUI中填写淘宝订单号")
    print("2. 发货完成后调用send_qianniu_delivery_message")
    print("3. 发送千牛图文消息")
    print("4. 检查是否有淘宝订单号")
    print("5. 如果有，调用千牛发货订单接口")
    print("6. 标记订单为已发货状态")
    print()
    
    print("🔧 技术实现:")
    print("- API地址: {api_url}/QianNiu/Api")
    print("- 请求方法: POST")
    print("- 参数: post=PostOrderShipment")
    print("- 数据: {userNick, orderId}")
    print("- 响应: {code: 200, data: {}, ret: ['SUCCESS::调用成功']}")
    print()
    
    print("⚠️ 注意事项:")
    print("1. 只有填写了淘宝订单号才会调用发货接口")
    print("2. 使用对应客服的API地址")
    print("3. 发货接口在图文消息发送成功后调用")
    print("4. 接口调用失败不影响主流程")
    
    return True

def main():
    """主测试函数"""
    print("千牛发货订单接口测试")
    print("验证淘宝订单发货功能")
    print()
    
    print("🔧 新增功能:")
    print("1. 检测淘宝订单号是否填写")
    print("2. 在千牛图文消息发送后调用发货接口")
    print("3. 使用动态API地址和客服信息")
    print("4. 标记淘宝订单为已发货状态")
    print()
    
    print("📋 接口规格:")
    print("URL: {api_url}/QianNiu/Api")
    print("Method: POST")
    print("Parameters:")
    print("  post: PostOrderShipment")
    print("  data: JSON字符串 {userNick, orderId}")
    print("Response:")
    print("  {code: 200, data: {}, ret: ['SUCCESS::调用成功']}")
    print()
    
    input("按回车键开始测试...")
    
    try:
        # 测试1：千牛发货接口格式
        print("\n🧪 测试1：千牛发货接口格式")
        test1_result = test_qianniu_shipment_api()
        
        # 测试2：dm_main中的发货方法
        print("\n🧪 测试2：dm_main中的发货方法")
        test2_result = test_dm_main_shipment_method()
        
        # 测试3：完整集成流程
        print("\n🧪 测试3：完整集成流程")
        test3_result = test_integration_flow()
        
        print("\n" + "=" * 60)
        print("测试结果总结")
        print("=" * 60)
        
        print(f"千牛发货接口格式: {'✅ 通过' if test1_result else '❌ 失败'}")
        print(f"dm_main发货方法: {'✅ 通过' if test2_result else '❌ 失败'}")
        print(f"完整集成流程: {'✅ 通过' if test3_result else '❌ 失败'}")
        
        if all([test1_result, test2_result, test3_result]):
            print("\n🎉 千牛发货订单接口功能测试通过！")
            print()
            print("📋 现在的完整流程:")
            print("1. 用户填写淘宝订单号")
            print("2. 发货完成后发送千牛图文消息")
            print("3. 自动调用千牛发货订单接口")
            print("4. 标记淘宝订单为已发货")
            print()
            print("🚀 功能已集成到发货流程中！")
        else:
            print("\n⚠️ 部分测试失败，请检查代码")
        
    except KeyboardInterrupt:
        print("\n测试被用户中断")
    except Exception as e:
        print(f"\n测试过程中发生错误: {str(e)}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
