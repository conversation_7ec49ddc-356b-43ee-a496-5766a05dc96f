#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
验证stop_event修复
"""

import sys
import os
import threading

# 添加当前目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_gamescript_creation():
    """测试GameScript创建"""
    print("测试GameScript创建...")
    
    try:
        from dm_main import GameScript
        
        # 测试1：无参数创建
        print("1. 无参数创建GameScript...")
        script1 = GameScript()
        print(f"   stop_event存在: {hasattr(script1, 'stop_event')}")
        if hasattr(script1, 'stop_event'):
            print(f"   stop_event类型: {type(script1.stop_event)}")
            print(f"   check_stop()结果: {script1.check_stop()}")
        
        # 测试2：带参数创建
        print("2. 带参数创建GameScript...")
        stop_event = threading.Event()
        script2 = GameScript(stop_event=stop_event)
        print(f"   stop_event存在: {hasattr(script2, 'stop_event')}")
        if hasattr(script2, 'stop_event'):
            print(f"   stop_event类型: {type(script2.stop_event)}")
            print(f"   check_stop()结果: {script2.check_stop()}")
        
        return True
        
    except Exception as e:
        print(f"测试失败: {str(e)}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("验证stop_event修复")
    print("=" * 40)
    
    result = test_gamescript_creation()
    
    if result:
        print("\n✅ 修复验证成功！")
        print("GameScript的stop_event属性现在应该正常工作了")
    else:
        print("\n❌ 修复验证失败")
        print("仍然存在问题，需要进一步调查")
