#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
测试订单状态检查功能的脚本
"""

import mysql.connector
from config import MYSQL_CONFIG
from mq_listener import <PERSON>QS<PERSON>
import time

def create_test_order(order_id, field3_value):
    """创建测试订单"""
    try:
        connection = mysql.connector.connect(**MYSQL_CONFIG)
        cursor = connection.cursor()
        
        # 插入测试订单
        query = """
        INSERT INTO orders (order_id, field3, created_at) 
        VALUES (%s, %s, NOW()) 
        ON DUPLICATE KEY UPDATE field3 = VALUES(field3)
        """
        cursor.execute(query, (order_id, field3_value))
        connection.commit()
        
        cursor.close()
        connection.close()
        
        print(f"✅ 创建测试订单: {order_id}, field3={field3_value}")
        return True
        
    except mysql.connector.Error as e:
        print(f"❌ 创建测试订单失败: {str(e)}")
        return False

def check_order_status(order_id):
    """检查订单状态"""
    try:
        connection = mysql.connector.connect(**MYSQL_CONFIG)
        cursor = connection.cursor()
        
        query = "SELECT field3 FROM orders WHERE order_id = %s"
        cursor.execute(query, (order_id,))
        result = cursor.fetchone()
        
        cursor.close()
        connection.close()
        
        if result is None:
            print(f"⚠️ 订单 {order_id} 不存在")
            return None
        else:
            field3_value = result[0]
            print(f"📊 订单 {order_id} 的field3值: {field3_value}")
            return field3_value
            
    except mysql.connector.Error as e:
        print(f"❌ 查询订单状态失败: {str(e)}")
        return None

def cleanup_test_orders():
    """清理测试订单"""
    try:
        connection = mysql.connector.connect(**MYSQL_CONFIG)
        cursor = connection.cursor()
        
        # 删除测试订单
        test_orders = ['TEST_ORDER_STATUS_1', 'TEST_ORDER_STATUS_2', 'TEST_ORDER_STATUS_3']
        for order_id in test_orders:
            cursor.execute("DELETE FROM orders WHERE order_id = %s", (order_id,))
        
        connection.commit()
        cursor.close()
        connection.close()
        
        print("🧹 清理测试订单完成")
        
    except mysql.connector.Error as e:
        print(f"⚠️ 清理测试订单失败: {str(e)}")

def test_order_status_check():
    """测试订单状态检查功能"""
    print("🧪 开始测试订单状态检查功能...")
    
    # 创建MQ发送器
    sender = MQSender()
    
    if not sender.connect():
        print("❌ 连接RabbitMQ失败")
        return False
    
    print("✅ 连接RabbitMQ成功")
    
    # 测试场景1：订单不存在
    print("\n📤 测试场景1：订单不存在")
    sender.send_test_message(
        order_id="TEST_ORDER_STATUS_1",
        region="梦江南",
        guild="帮1",
        appearance="测试外观1",
        character_name="测试角色1"
    )
    
    time.sleep(3)
    
    # 测试场景2：订单存在但field3=3（已完成）
    print("\n📤 测试场景2：订单已完成(field3=3)")
    create_test_order("TEST_ORDER_STATUS_2", 3)
    sender.send_test_message(
        order_id="TEST_ORDER_STATUS_2",
        region="梦江南",
        guild="帮1",
        appearance="测试外观2",
        character_name="测试角色2"
    )
    
    time.sleep(3)
    
    # 测试场景3：订单存在且field3!=3（需要处理）
    print("\n📤 测试场景3：订单需要处理(field3=1)")
    create_test_order("TEST_ORDER_STATUS_3", 1)
    sender.send_test_message(
        order_id="TEST_ORDER_STATUS_3",
        region="梦江南",
        guild="帮1",
        appearance="测试外观3",
        character_name="测试角色3"
    )
    
    # 关闭连接
    sender.close()
    
    print("\n🎯 测试完成！")
    print("请观察dm_main.py的日志输出，验证订单状态检查逻辑是否正确工作")
    
    # 清理测试数据
    print("\n🧹 清理测试数据...")
    cleanup_test_orders()
    
    return True

if __name__ == "__main__":
    test_order_status_check()
